version: "3"
services:
  fin_notebook-db:
    image: "postgres:14.6"
    container_name: fin_notebook-db
    volumes:
      - fin_notebook_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5438:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - TZ="Europe/Istanbul"

  fin_notebook:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: fin_notebook
    environment:
      - TZ="Europe/Istanbul"
      - DEV_MODE=true
    container_name: fin_notebook
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8008:8008
    depends_on:
      - fin_notebook-db

volumes:
  fin_notebook_data:

networks:
  main:
    name: main_network
    driver: bridge