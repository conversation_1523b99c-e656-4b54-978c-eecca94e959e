package routes

import (
	"github.com/NocyTech/fin_notebook/pkg/domains/transaction"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func TransactionRoutes(r *gin.RouterGroup, s transaction.Service) {
	g := r.Group("/transactions")
	g.Use(middleware.Authorized())

	g.POST("", CreateTransaction(s))
	g.GET("", GetAllTransactions(s))
	g.GET("/:id", GetTransactionByID(s))
	g.PUT("/:id", UpdateTransaction(s))
	g.DELETE("/:id", DeleteTransaction(s))
}

// @Summary Create transaction
// @Description Create a new income or expense transaction
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.TransactionRequest true "Transaction data"
// @Success 201 {object} map[string]interface{} "Returns created transaction"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /transactions [post]
func CreateTransaction(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.TransactionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.CreateTransaction(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get all transactions
// @Description Get all transactions with optional filtering
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Filter by type (income/expense)"
// @Param start_date query string false "Filter by start date (YYYY-MM-DD)"
// @Param end_date query string false "Filter by end date (YYYY-MM-DD)"
// @Param category_id query string false "Filter by category ID"
// @Param payment_method query string false "Filter by payment method"
// @Param account_id query string false "Filter by account ID"
// @Param min_amount query number false "Filter by minimum amount"
// @Param max_amount query number false "Filter by maximum amount"
// @Param search query string false "Search in title and note"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} map[string]interface{} "Returns list of transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /transactions [get]
func GetAllTransactions(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var filter dtos.TransactionFilterRequest
		if err := c.ShouldBindQuery(&filter); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetAllTransactions(userID.String(), &filter)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get transaction by ID
// @Description Get a specific transaction by its ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} map[string]interface{} "Returns transaction details"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/{id} [get]
func GetTransactionByID(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetTransactionByID(id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Update transaction
// @Description Update an existing transaction
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Param request body dtos.TransactionUpdateRequest true "Updated transaction data"
// @Success 200 {object} map[string]interface{} "Returns updated transaction"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/{id} [put]
func UpdateTransaction(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		var req dtos.TransactionUpdateRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateTransaction(id, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete transaction
// @Description Delete a transaction by ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/{id} [delete]
func DeleteTransaction(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		if err := s.DeleteTransaction(id); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "transaction deleted successfully",
			"status": 200,
		})
	}
}
