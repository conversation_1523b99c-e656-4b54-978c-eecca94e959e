package dtos

type BudgetRequest struct {
	Amount     float64 `json:"amount" binding:"required,gt=0"`
	Period     string  `json:"period" binding:"required,oneof=monthly yearly"`
	CategoryId string  `json:"category_id" binding:"required,uuid"`
}

type BudgetResponse struct {
	ID           string  `json:"id"`
	Amount       float64 `json:"amount"`
	Period       string  `json:"period"`
	CategoryId   string  `json:"category_id"`
	CategoryName string  `json:"category_name"`
	Spent        float64 `json:"spent"`
	CreatedAt    string  `json:"created_at"`
	UpdatedAt    string  `json:"updated_at"`
}
