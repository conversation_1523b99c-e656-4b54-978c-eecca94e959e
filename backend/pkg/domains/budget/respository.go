package budget

import (
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	Create(budget *entities.Budget) error
	Update(id uuid.UUID, req dtos.BudgetRequest) error
	UpdateWithUserCheck(userID uuid.UUID, id uuid.UUID, req dtos.BudgetRequest) error
	Delete(id uuid.UUID) error
	DeleteWithUserCheck(userID uuid.UUID, id uuid.UUID) error
	FindAll(userId uuid.UUID) ([]dtos.BudgetResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(budget *entities.Budget) error {
	return r.db.Create(budget).Error
}

func (r *repository) Update(id uuid.UUID, req dtos.BudgetRequest) error {
	var budget entities.Budget
	err := r.db.Model(&entities.Budget{}).Where("id = ?", id).First(&budget).Error
	if err != nil {
		return err
	}
	if req.Amount != 0 {
		budget.Amount = req.Amount
	}
	if req.Period != "" {
		budget.Period = req.Period
	}
	if req.CategoryId != "" {
		categoryUUID, err := uuid.Parse(req.CategoryId)
		if err != nil {
			return err
		}
		budget.CategoryId = categoryUUID
	}

	return r.db.Save(&budget).Error
}

func (r *repository) UpdateWithUserCheck(userID uuid.UUID, id uuid.UUID, req dtos.BudgetRequest) error {
	var budget entities.Budget
	err := r.db.Model(&entities.Budget{}).Where("id = ? AND user_id = ?", id, userID).First(&budget).Error
	if err != nil {
		return err
	}
	if req.Amount != 0 {
		budget.Amount = req.Amount
	}
	if req.Period != "" {
		budget.Period = req.Period
	}
	if req.CategoryId != "" {
		categoryUUID, err := uuid.Parse(req.CategoryId)
		if err != nil {
			return err
		}
		budget.CategoryId = categoryUUID
	}

	return r.db.Save(&budget).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Budget{}, "id = ?", id).Error
}

func (r *repository) DeleteWithUserCheck(userID uuid.UUID, id uuid.UUID) error {
	return r.db.Delete(&entities.Budget{}, "id = ? AND user_id = ?", id, userID).Error
}

func (r *repository) FindAll(userId uuid.UUID) ([]dtos.BudgetResponse, error) {
	var budgets []entities.Budget
	var transactions []entities.Transaction
	var categories []entities.Category
	var responses []dtos.BudgetResponse

	if err := r.db.Where("user_id = ?", userId).Find(&budgets).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	if err := r.db.Where("user_id = ? AND type = 'expense'", userId).Find(&transactions).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	if err := r.db.Find(&categories).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	// Create category map for quick lookup
	categoryMap := make(map[uuid.UUID]string)
	for _, cat := range categories {
		categoryMap[cat.ID] = cat.Name
	}

	for _, budget := range budgets {
		var spent float64

		for _, tx := range transactions {
			if tx.CategoryID == budget.CategoryId {
				spent += tx.Amount
			}
		}

		categoryName := categoryMap[budget.CategoryId]
		if categoryName == "" {
			categoryName = "Unknown Category"
		}

		responses = append(responses, dtos.BudgetResponse{
			ID:           budget.ID.String(),
			Amount:       budget.Amount,
			Period:       budget.Period,
			CategoryId:   budget.CategoryId.String(),
			CategoryName: categoryName,
			Spent:        spent,
			CreatedAt:    budget.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt:    budget.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		})
	}

	return responses, nil
}
