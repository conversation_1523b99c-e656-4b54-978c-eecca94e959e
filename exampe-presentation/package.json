{"name": "presentations_page", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.11.0", "i18next": "^23.0.0", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^13.0.0", "react-router-dom": "^7.8.1", "react-scripts": "^5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^4.9.5"}}