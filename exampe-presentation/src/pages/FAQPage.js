import React, { useState } from 'react';

const FAQPage = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  const faqs = [
    {
      category: "Genel Sorular",
      questions: [
        {
          question: "WaChatify nedir ve nasıl çalışır?",
          answer: "WaChatify, WhatsApp üzerinden toplu mesaj gönderimi yapmanızı sağlayan profesyonel bir platformdur. WhatsApp Web API'sini kullanarak binlerce kişiye aynı anda kişiselleştirilmiş mesajlar gönderebilirsiniz."
        },
        {
          question: "WhatsApp hesabım bloke olur mu?",
          answer: "WaChatify, WhatsApp'ın resmi API'sini kullanır ve güvenlik protokollerine uyar. Makul gönderim hızları ve spam önleme sistemleri ile hesabınızı korur. Ancak aşırı kullanım durumunda WhatsApp tarafından geçici kısıtlamalar uygulanabilir."
        },
        {
          question: "Hangi dosya formatlarını destekliyorsunuz?",
          answer: "Metin mesajları, resimler (JPG, PNG), videolar (MP4), ses dosyaları (MP3), PDF dokümanları ve Excel dosyalarını destekliyoruz. Dosya boyutu limiti 16MB'dır."
        },
        {
          question: "Kaç kişiye aynı anda mesaj gönderebilirim?",
          answer: "Paketinize göre değişir. Başlangıç paketinde günde 1000, profesyonel paketlerde günde 10.000+ mesaj gönderebilirsiniz. Güvenlik için saatlik limitler de mevcuttur."
        }
      ]
    },
    {
      category: "Kurulum ve Kullanım",
      questions: [
        {
          question: "Kurulum ne kadar sürer?",
          answer: "Hesap oluşturma ve WhatsApp bağlantısı 5 dakikada tamamlanır. İlk mesajınızı 10 dakika içinde gönderebilirsiniz. Teknik bilgi gerektirmez."
        },
        {
          question: "Birden fazla WhatsApp hesabı kullanabilir miyim?",
          answer: "Evet, cihaz paketleri satın alarak birden fazla WhatsApp hesabını aynı anda kullanabilirsiniz. Her hesap için ayrı QR kod taraması gerekir."
        },
        {
          question: "Kişi listemi nasıl içe aktarırım?",
          answer: "Excel veya CSV dosyasından toplu içe aktarım yapabilir, manuel olarak tek tek ekleyebilir veya mevcut WhatsApp gruplarınızdan kişileri çekebilirsiniz."
        },
        {
          question: "Mesajlarımı zamanla gönderebilir miyim?",
          answer: "Evet, istediğiniz tarih ve saatte mesajlarınızın gönderilmesi için zamanlama yapabilirsiniz. Kampanyalarınızı önceden planlayabilirsiniz."
        }
      ]
    },
    {
      category: "Fiyatlandırma ve Ödeme",
      questions: [
        {
          question: "Ücretsiz deneme nasıl çalışır?",
          answer: "7 gün boyunca tüm özellikleri ücretsiz kullanabilirsiniz. Kredi kartı bilgisi gerekmez. Deneme süresi sonunda istediğiniz paketi seçebilirsiniz."
        },
        {
          question: "Mesaj kredileri nasıl çalışır?",
          answer: "Her gönderilen mesaj için 1 kredi harcanır. Krediler hesabınızda birikir ve istediğiniz zaman kullanabilirsiniz. Kredi süresi dolmaz."
        },
        {
          question: "Paket değişikliği yapabilir miyim?",
          answer: "Evet, istediğiniz zaman paketinizi yükseltebilir veya düşürebilirsiniz. Değişiklik anında geçerli olur ve kalan kredileriniz korunur."
        },
        {
          question: "Hangi ödeme yöntemlerini kabul ediyorsunuz?",
          answer: "Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. Tüm ödemeler SSL ile güvence altındadır. PayTR güvenli ödeme sistemi kullanıyoruz."
        },
        {
          question: "İptal etmek istersem ne olur?",
          answer: "İstediğiniz zaman iptal edebilirsiniz. Kalan kredileriniz 1 yıl boyunca hesabınızda kalır ve tekrar aktifleştirdiğinizde kullanabilirsiniz."
        }
      ]
    },
    {
      category: "Teknik Destek",
      questions: [
        {
          question: "Hangi cihazlarda çalışır?",
          answer: "Windows, Mac, Linux bilgisayarlar ve Android, iOS mobil cihazlarda çalışır. Web tabanlı olduğu için herhangi bir program indirmenize gerek yoktur."
        },
        {
          question: "İnternet bağlantım kesilirse ne olur?",
          answer: "Gönderim işlemi sunucularımızda devam eder. Bağlantınız tekrar kurulduğunda kaldığınız yerden devam edebilirsiniz."
        },
        {
          question: "Verilerim güvende mi?",
          answer: "Evet, tüm verileriniz SSL şifreleme ile korunur. KVKK uyumlu çalışıyoruz ve verilerinizi üçüncü taraflarla paylaşmıyoruz."
        },
        {
          question: "Destek nasıl alabilirim?",
          answer: "7/24 WhatsApp destek hattımız, e-posta desteği ve canlı chat sistemi mevcuttur. Ayrıca detaylı kullanım kılavuzları ve video eğitimler sunuyoruz."
        }
      ]
    },
    {
      category: "Özellikler",
      questions: [
        {
          question: "Kişiselleştirilmiş mesajlar gönderebilir miyim?",
          answer: "Evet, {isim}, {şirket}, {telefon} gibi değişken alanlar kullanarak her kişiye özel mesajlar gönderebilirsiniz."
        },
        {
          question: "Gönderim raporları nasıl çalışır?",
          answer: "Detaylı raporlarda gönderim başarı oranları, açılma oranları, tıklama oranları ve hata mesajlarını görebilirsiniz. Excel formatında dışa aktarabilirsiniz."
        },
        {
          question: "Blacklist özelliği var mı?",
          answer: "Evet, istemediğiniz numaraları blacklist'e ekleyerek bu kişilere mesaj gönderilmesini engelleyebilirsiniz."
        },
        {
          question: "API entegrasyonu mevcut mu?",
          answer: "Pro paketlerde REST API erişimi mevcuttur. Kendi sisteminizle entegrasyon yapabilir ve otomatik mesaj gönderimlerini programlayabilirsiniz."
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Sıkça Sorulan
              <span className="text-blue-600 block">Sorular</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              WaChatify hakkında merak ettiklerinizin yanıtlarını burada bulabilirsiniz. 
              Aradığınızı bulamazsanız bizimle iletişime geçin.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="https://wa.me/905417173986" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-green-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-green-600 transition-colors"
              >
                WhatsApp Destek
              </a>
              <a 
                href="/iletisim" 
                className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
              >
                İletişim Formu
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Sections */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {faqs.map((category, categoryIndex) => (
              <div key={categoryIndex} className="mb-16">
                <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                  {category.category}
                </h2>
                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => {
                    const globalIndex = categoryIndex * 100 + faqIndex;
                    return (
                      <div 
                        key={globalIndex}
                        className="bg-gray-50 rounded-lg overflow-hidden"
                      >
                        <button
                          onClick={() => toggleFAQ(globalIndex)}
                          className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors"
                        >
                          <span className="text-lg font-semibold text-gray-900">
                            {faq.question}
                          </span>
                          <svg 
                            className={`w-6 h-6 text-gray-500 transform transition-transform ${
                              openFAQ === globalIndex ? 'rotate-180' : ''
                            }`}
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                        {openFAQ === globalIndex && (
                          <div className="px-6 pb-4">
                            <p className="text-gray-600 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Aradığınız Cevabı Bulamadınız mı?
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Uzman ekibimiz size yardımcı olmak için burada. 
              Hemen iletişime geçin ve sorularınızın yanıtını alın.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <a 
                href="https://wa.me/905417173986" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-green-500 text-white p-6 rounded-lg hover:bg-green-600 transition-colors"
              >
                <svg className="w-8 h-8 mx-auto mb-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.087"/>
                </svg>
                <h3 className="font-semibold mb-2">WhatsApp Destek</h3>
                <p className="text-sm opacity-90">Anında cevap alın</p>
              </a>

              <a 
                href="mailto:<EMAIL>" 
                className="bg-blue-500 text-white p-6 rounded-lg hover:bg-blue-600 transition-colors"
              >
                <svg className="w-8 h-8 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h3 className="font-semibold mb-2">E-posta Desteği</h3>
                <p className="text-sm opacity-90">Detaylı sorular için</p>
              </a>

              <a 
                href="tel:+905417173986" 
                className="bg-purple-500 text-white p-6 rounded-lg hover:bg-purple-600 transition-colors"
              >
                <svg className="w-8 h-8 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <h3 className="font-semibold mb-2">Telefon Desteği</h3>
                <p className="text-sm opacity-90">Sesli görüşme</p>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-3xl font-bold mb-6">
              Hemen Başlayın!
            </h2>
            <p className="text-xl mb-8 opacity-90">
              7 gün ücretsiz deneme ile WaChatify'ı test edin. 
              Sorularınız varsa destek ekibimiz size yardımcı olur.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://app.wachatify.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Ücretsiz Başla
              </a>
              <a 
                href="https://wa.me/905417173986" 
                target="_blank" 
                rel="noopener noreferrer"
                className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Destek Al
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FAQPage;
