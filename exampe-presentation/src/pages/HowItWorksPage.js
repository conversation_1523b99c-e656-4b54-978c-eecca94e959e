import React from 'react';
import HowItWorks from '../components/HowItWorks';

const HowItWorksPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              WaChatify
              <span className="text-blue-600 block">Nasıl Çalışır?</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Sadece 4 basit adımda WhatsApp toplu mesaj gönderimini başlatın. 
              Teknik bilgi gerektirmez, dakikalar içinde kullanmaya başlayın.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Component */}
      <HowItWorks />

      {/* Detailed Steps */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Detaylı Kullanım Kılavuzu
              </h2>
              <p className="text-lg text-gray-600">
                Her adımı detaylarıyla öğrenin ve profesyonel sonuçlar alın
              </p>
            </div>

            <div className="space-y-16">
              {/* Step 1 Detail */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mr-4">
                      1
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Hesap Oluşturma ve Kurulum</h3>
                  </div>
                  <div className="space-y-4 text-gray-600">
                    <p>• E-posta adresinizle ücretsiz hesap oluşturun</p>
                    <p>• WhatsApp QR kodunu tarayarak cihazınızı bağlayın</p>
                    <p>• Güvenlik ayarlarınızı yapılandırın</p>
                    <p>• İlk mesaj kredilerinizi alın</p>
                  </div>
                </div>
                <div className="bg-white p-8 rounded-2xl shadow-lg">
                  <div className="text-center">
                    <div className="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">Hızlı Kurulum</h4>
                    <p className="text-gray-600">5 dakikada hesabınız hazır</p>
                  </div>
                </div>
              </div>

              {/* Step 2 Detail */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="order-2 lg:order-1">
                  <div className="bg-white p-8 rounded-2xl shadow-lg">
                    <div className="text-center">
                      <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Kolay İçe Aktarım</h4>
                      <p className="text-gray-600">Excel, CSV veya manuel ekleme</p>
                    </div>
                  </div>
                </div>
                <div className="order-1 lg:order-2">
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mr-4">
                      2
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Kişi Listesi Hazırlama</h3>
                  </div>
                  <div className="space-y-4 text-gray-600">
                    <p>• Excel veya CSV dosyasından toplu içe aktarım</p>
                    <p>• Manuel olarak tek tek kişi ekleme</p>
                    <p>• Gruplar oluşturarak kişileri kategorize etme</p>
                    <p>• Geçersiz numaraları otomatik filtreleme</p>
                  </div>
                </div>
              </div>

              {/* Step 3 Detail */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mr-4">
                      3
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Mesaj Hazırlama</h3>
                  </div>
                  <div className="space-y-4 text-gray-600">
                    <p>• Kişiselleştirilmiş mesaj şablonları oluşturun</p>
                    <p>• Resim, video, PDF dosyaları ekleyin</p>
                    <p>• Değişken alanlar ile özel mesajlar</p>
                    <p>• Mesaj önizlemesi ile kontrol edin</p>
                  </div>
                </div>
                <div className="bg-white p-8 rounded-2xl shadow-lg">
                  <div className="text-center">
                    <div className="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">Zengin İçerik</h4>
                    <p className="text-gray-600">Metin, medya ve kişiselleştirme</p>
                  </div>
                </div>
              </div>

              {/* Step 4 Detail */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="order-2 lg:order-1">
                  <div className="bg-white p-8 rounded-2xl shadow-lg">
                    <div className="text-center">
                      <div className="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Anında Gönderim</h4>
                      <p className="text-gray-600">Hızlı ve güvenilir teslimat</p>
                    </div>
                  </div>
                </div>
                <div className="order-1 lg:order-2">
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mr-4">
                      4
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Gönderim ve Takip</h3>
                  </div>
                  <div className="space-y-4 text-gray-600">
                    <p>• Anında gönderim veya zamanlı gönderim seçenekleri</p>
                    <p>• Gerçek zamanlı gönderim durumu takibi</p>
                    <p>• Detaylı raporlar ve analitikler</p>
                    <p>• Başarısız gönderimler için yeniden deneme</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Sıkça Sorulan Sorular
              </h2>
              <p className="text-lg text-gray-600">
                WaChatify kullanımı hakkında merak ettikleriniz
              </p>
            </div>

            <div className="space-y-8">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  WhatsApp hesabım bloke olur mu?
                </h3>
                <p className="text-gray-600">
                  WaChatify, WhatsApp'ın resmi API'sini kullanır ve güvenlik protokollerine uyar. 
                  Makul gönderim hızları ve spam önleme sistemleri ile hesabınızı korur.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Kaç kişiye aynı anda mesaj gönderebilirim?
                </h3>
                <p className="text-gray-600">
                  Paketinize göre değişir. Başlangıç paketinde günde 1000, profesyonel paketlerde 
                  günde 10.000+ mesaj gönderebilirsiniz.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Kurulum ne kadar sürer?
                </h3>
                <p className="text-gray-600">
                  Hesap oluşturma ve WhatsApp bağlantısı 5 dakikada tamamlanır. 
                  İlk mesajınızı 10 dakika içinde gönderebilirsiniz.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-3xl font-bold mb-6">
              Hemen Başlayın!
            </h2>
            <p className="text-xl mb-8 opacity-90">
              7 gün ücretsiz deneme ile WaChatify'ı test edin. 
              Kurulum sadece 5 dakika.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://app.wachatify.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Ücretsiz Başla
              </a>
              <a 
                href="https://wa.me/905417173986" 
                target="_blank" 
                rel="noopener noreferrer"
                className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Destek Al
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HowItWorksPage;
