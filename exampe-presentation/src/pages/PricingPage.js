import React from 'react';
import Pricing from '../components/Pricing';

const PricingPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Uygun Fiyatlı
              <span className="text-blue-600 block"><PERSON><PERSON> Seçenekleri</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              İhtiyacınıza uygun paketi seçin ve WhatsApp toplu mesaj gönderiminin gücünü keşfedin. 
              Tüm paketlerde 7 gün ücretsiz deneme imkanı.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="bg-green-100 text-green-800 px-6 py-3 rounded-full font-semibold">
                ✓ 7 Gün Ücretsiz Deneme
              </div>
              <div className="bg-blue-100 text-blue-800 px-6 py-3 rounded-full font-semibold">
                ✓ Kredi Kartı Gerekmez
              </div>
              <div className="bg-purple-100 text-purple-800 px-6 py-3 rounded-full font-semibold">
                ✓ Anında Aktivasyon
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Component */}
      <Pricing />

      {/* Comparison Table */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Paket Karşılaştırması
              </h2>
              <p className="text-lg text-gray-600">
                Hangi paketin size uygun olduğunu görün
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full bg-white rounded-lg shadow-lg overflow-hidden">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Özellikler</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Tanışma</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Mini</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Eko</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Pro</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">Mesaj Kredisi</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">2,500 - 100,000</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">500</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">2,500</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">10,000</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 text-sm text-gray-900">WhatsApp Cihaz Sayısı</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">1</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">1</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">1</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">1</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">Medya Desteği</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 text-sm text-gray-900">Zamanlı Gönderim</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">Detaylı Raporlama</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-400">Temel</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-400">Temel</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">Gelişmiş</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">Premium</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 text-sm text-gray-900">API Erişimi</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-400">✗</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-400">✗</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-400">✗</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">✓</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">Destek</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">E-posta</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">E-posta</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600">Öncelikli</td>
                    <td className="px-6 py-4 text-center text-sm text-green-600">7/24</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Fiyatlandırma Hakkında SSS
              </h2>
              <p className="text-lg text-gray-600">
                Paketler ve ödeme hakkında sık sorulan sorular
              </p>
            </div>

            <div className="space-y-8">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Ücretsiz deneme nasıl çalışır?
                </h3>
                <p className="text-gray-600">
                  7 gün boyunca tüm özellikleri ücretsiz kullanabilirsiniz. Kredi kartı bilgisi gerekmez. 
                  Deneme süresi sonunda istediğiniz paketi seçebilirsiniz.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Mesaj kredileri nasıl çalışır?
                </h3>
                <p className="text-gray-600">
                  Her gönderilen mesaj için 1 kredi harcanır. Krediler hesabınızda birikir ve 
                  istediğiniz zaman kullanabilirsiniz. Kredi süresi dolmaz.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Paket değişikliği yapabilir miyim?
                </h3>
                <p className="text-gray-600">
                  Evet, istediğiniz zaman paketinizi yükseltebilir veya düşürebilirsiniz. 
                  Değişiklik anında geçerli olur ve kalan kredileriniz korunur.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Hangi ödeme yöntemlerini kabul ediyorsunuz?
                </h3>
                <p className="text-gray-600">
                  Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. 
                  Tüm ödemeler SSL ile güvence altındadır.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  İptal etmek istersem ne olur?
                </h3>
                <p className="text-gray-600">
                  İstediğiniz zaman iptal edebilirsiniz. Kalan kredileriniz 1 yıl boyunca 
                  hesabınızda kalır ve tekrar aktifleştirdiğinizde kullanabilirsiniz.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-3xl font-bold mb-6">
              Hemen Başlayın ve Farkı Görün!
            </h2>
            <p className="text-xl mb-8 opacity-90">
              7 gün ücretsiz deneme ile WaChatify'ın gücünü keşfedin. 
              Kredi kartı bilgisi gerekmez.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://app.wachatify.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                7 Gün Ücretsiz Dene
              </a>
              <a 
                href="https://wa.me/905417173986" 
                target="_blank" 
                rel="noopener noreferrer"
                className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Fiyat Teklifi Al
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingPage;
