.App {
  text-align: center;
}

/* Global Styles */
.mobile-image {
  display: none !important;
}

p {
  color: #000 !important;
}

.headtop {
  background-color: #066bb9;
}

.headtopright .lang select {
  background-color: #066bb9;
}

.sectionappbar h2 {
  color: #066bb9;
}

.tab-content-span {
  color: #000 !important;
}

.qr-code-container {
  background-color: #fff;
}

.sectionappbar .img {
  height: auto !important;
}

@media(max-width:950px) {
  .blogSwiper .swiper-slide-prev,
  .hmblogitem,
  .swiper,
  .blogSwiper .swiper-slide-next {
    overflow: inherit !important;
  }

  .desktop-blog {
    display: none;
  }
}

@media (max-width:768px) {
  .blogSwiper .swiper-slide-active .hmblogitem .text p {
    margin-bottom: 0 !important;
  }

  .blogSwiper .swiper-slide-active .hmblogitem .text .devamini-oku {
    display: block;
    margin-inline: auto;
    width: 100%;
    margin-top: 20px !important;
  }

  .qr-code-title {
    font-size: 24px !important;
    margin: 0 !important;
  }

  .qr-code-img {
    max-width: 200px !important;
  }

  .desktop-image {
    display: none !important;
  }

  .mobile-image {
    display: block !important;
  }

  .qr-code-container {
    background-color: #e4f4f5;
  }
}

/* Notification Styles */
#snackbar {
  visibility: hidden;
  min-width: 250px;
  margin-left: -125px;
  background-color: white;
  border: 1px solid red;
  color: red;
  border-radius: 5px;
  text-align: center;
  padding: 20px;
  position: fixed;
  z-index: 1;
  right: 30px;
  top: 135px;
  font-size: 17px;
}

#snackbar.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

#snackbarTrue {
  visibility: hidden;
  min-width: 250px;
  margin-left: -125px;
  background-color: white;
  border: 1px solid green;
  color: green;
  border-radius: 5px;
  text-align: center;
  padding: 20px;
  position: fixed;
  z-index: 1;
  right: 30px;
  top: 135px;
  font-size: 17px;
}

#snackbarTrue.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
  from {
    right: 0;
    opacity: 0;
  }
  to {
    right: 30px;
    opacity: 1;
  }
}

@keyframes fadein {
  from {
    right: 0;
    opacity: 0;
  }
  to {
    right: 30px;
    opacity: 1;
  }
}

@-webkit-keyframes fadeout {
  from {
    right: 30px;
    opacity: 1;
  }
  to {
    right: 0;
    opacity: 0;
  }
}

@keyframes fadeout {
  from {
    right: 30px;
    opacity: 1;
  }
  to {
    right: 0;
    opacity: 0;
  }
}

/* WhatsApp Floating Button */
.whatsapp-float {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 40px;
  right: 40px;
  background-color: #25d366;
  color: #FFF;
  border-radius: 50px;
  text-align: center;
  font-size: 30px;
  box-shadow: 2px 2px 3px #999;
  z-index: 100;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.whatsapp-float:hover {
  background-color: #128c7e;
  transform: scale(1.1);
  color: #FFF;
  text-decoration: none;
}

.whatsapp-float svg {
  width: 32px;
  height: 32px;
}

@media screen and (max-width: 767px) {
  .whatsapp-float {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
    font-size: 24px;
  }

  .whatsapp-float svg {
    width: 28px;
    height: 28px;
  }
}
