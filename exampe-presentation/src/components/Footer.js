import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      {/* App Download Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center justify-between">
            <div className="lg:w-1/2 mb-8 lg:mb-0">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4">
                Uygulamayı İndirip Ücretsiz Kullanmaya Başlayın!
              </h3>
              
              <div className="flex flex-wrap gap-4">
                <a href="https://apps.apple.com/app/wachatify" target="_blank" rel="noopener noreferrer" className="inline-block">
                  <img src="/app-store.png" alt="App Store" className="h-12" />
                </a>
                <a href="https://play.google.com/store/apps/details?id=com.wachatify" target="_blank" rel="noopener noreferrer" className="inline-block">
                  <img src="/google-play.png" alt="Google Play" className="h-12" />
                </a>
                <a href="https://appgallery.huawei.com/app/wachatify" target="_blank" rel="noopener noreferrer" className="inline-block">
                  <img src="/app-gallery.png" alt="AppGallery" className="h-12" />
                </a>
                <Link 
                  to="/register" 
                  className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center"
                >
                  Hemen Dene
                </Link>
              </div>
            </div>

            <div className="lg:w-1/2 text-center lg:text-right">
              <h4 className="text-xl font-bold mb-4">Bizi Sosyal Medyada Takip Edin!</h4>
              <div className="flex justify-center lg:justify-end space-x-4">
                <a href="https://twitter.com/wachatify" target="_blank" rel="noopener noreferrer" className="bg-white bg-opacity-20 p-3 rounded-full hover:bg-opacity-30 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="https://facebook.com/wachatify" target="_blank" rel="noopener noreferrer" className="bg-white bg-opacity-20 p-3 rounded-full hover:bg-opacity-30 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
                <a href="https://instagram.com/wachatify" target="_blank" rel="noopener noreferrer" className="bg-white bg-opacity-20 p-3 rounded-full hover:bg-opacity-30 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                  </svg>
                </a>
                <a href="https://linkedin.com/company/wachatify" target="_blank" rel="noopener noreferrer" className="bg-white bg-opacity-20 p-3 rounded-full hover:bg-opacity-30 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="https://github.com/wachatify" target="_blank" rel="noopener noreferrer" className="bg-white bg-opacity-20 p-3 rounded-full hover:bg-opacity-30 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.5.75C6.146.75 1 5.896 1 12.25c0 5.089 3.292 9.387 7.863 10.956.575-.105 1.413-.251 1.413-1.126 0-.553-.004-2.008-.004-3.935-3.203.695-3.875-1.548-3.875-1.548-.522-1.319-1.274-1.669-1.274-1.669-1.043-.713.079-.698.079-.698 1.153.081 1.76 1.184 1.76 1.184 1.025 1.755 2.688 1.248 3.344.954.104-.741.4-1.248.728-1.538-2.555-.29-5.24-1.279-5.24-5.69 0-1.255.45-2.279 1.184-3.085-.119-.29-.513-1.458.112-3.04 0 0 .963-.309 3.158 1.179.916-.255 1.899-.382 2.876-.387.976.005 1.96.132 2.876.387 2.195-1.488 3.158-1.179 3.158-1.179.625 1.582.231 2.75.112 3.04.734.806 1.184 1.83 1.184 3.085 0 4.421-2.69 5.395-5.252 5.68.413.355.78 1.058.78 2.132 0 1.54-.004 2.781-.004 3.157 0 .88.838 1.026 1.413 1.126C19.708 21.637 23 17.339 23 12.25 23 5.896 17.854.75 11.5.75z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-4">Kurumsal</h3>
              <ul className="space-y-2">
                <li><Link to="/hakkimizda" className="text-gray-300 hover:text-white transition-colors">Biz Kimiz?</Link></li>
                <li><Link to="/belgelerimiz" className="text-gray-300 hover:text-white transition-colors">Belgelerimiz</Link></li>
                <li><Link to="/register" className="text-gray-300 hover:text-white transition-colors">Kayıt Ol</Link></li>
                <li><Link to="/login" className="text-gray-300 hover:text-white transition-colors">Giriş Yap</Link></li>
                <li><Link to="/forgot-password" className="text-gray-300 hover:text-white transition-colors">Şifremi Unuttum</Link></li>
                <li><Link to="/terms" className="text-gray-300 hover:text-white transition-colors">Hizmet Sözleşmesi</Link></li>
              </ul>
            </div>

            {/* WhatsApp Bot */}
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-4">Whatsapp Robotu</h3>
              <ul className="space-y-2">
                <li><Link to="/nedir" className="text-gray-300 hover:text-white transition-colors">Nedir?</Link></li>
                <li><Link to="/nasil-calisir" className="text-gray-300 hover:text-white transition-colors">Nasıl Çalışır?</Link></li>
                <li><Link to="/sss" className="text-gray-300 hover:text-white transition-colors">Sıkça Sorulan Sorular</Link></li>
                <li><Link to="/blog" className="text-gray-300 hover:text-white transition-colors">Blog</Link></li>
              </ul>
            </div>

            {/* Features */}
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-4">Özellikler</h3>
              <ul className="space-y-2">
                <li><Link to="/whatsapp-api" className="text-gray-300 hover:text-white transition-colors">Whatsapp Api</Link></li>
                <li><Link to="/ozellikler" className="text-gray-300 hover:text-white transition-colors">Tüm Özellikler</Link></li>
                <li><Link to="/rehber-aktar" className="text-gray-300 hover:text-white transition-colors">Rehberini Aktar</Link></li>
                <li><Link to="/coklu-hesap" className="text-gray-300 hover:text-white transition-colors">Çoklu Hesap</Link></li>
                <li><Link to="/limitsiz-gonderim" className="text-gray-300 hover:text-white transition-colors">Limitsiz Gönderim</Link></li>
                <li><Link to="/whatsapp-rehberi" className="text-gray-300 hover:text-white transition-colors">Whatsapp Rehberi</Link></li>
                <li><Link to="/otomatik-cevap" className="text-gray-300 hover:text-white transition-colors">Otomatik Cevap Botu</Link></li>
                <li><Link to="/ileri-tarihli-kampanya" className="text-gray-300 hover:text-white transition-colors">İleri Tarihli Kampanya</Link></li>
                <li><Link to="/kisisellestirilmis-mesajlar" className="text-gray-300 hover:text-white transition-colors">Kişiye Özel Mesajlar</Link></li>
                <li><Link to="/dosya-yukleme" className="text-gray-300 hover:text-white transition-colors">Dosya Yükleme</Link></li>
                <li><Link to="/detayli-rapor" className="text-gray-300 hover:text-white transition-colors">Detaylı Gönderim Raporu</Link></li>
                <li><Link to="/bloke-onleyici" className="text-gray-300 hover:text-white transition-colors">Bloke Önleyici Sistem</Link></li>
                <li><Link to="/coklu-dil" className="text-gray-300 hover:text-white transition-colors">Çoklu Dil Desteği</Link></li>
                <li><Link to="/grup-mesaj" className="text-gray-300 hover:text-white transition-colors">Gruplara Mesaj Gönderimi</Link></li>
                <li><Link to="/kurumsal-whatsapp" className="text-gray-300 hover:text-white transition-colors">Kurumsal WhatsApp Mesajlaşma</Link></li>
              </ul>
            </div>

            {/* Who Can Use */}
            <div className="lg:col-span-1">
              <h3 className="text-xl font-bold mb-4">Kimler Kullanabilir?</h3>
              <ul className="space-y-2">
                <li><Link to="/dernekler" className="text-gray-300 hover:text-white transition-colors">Dernekler</Link></li>
                <li><Link to="/satis-ekipleri" className="text-gray-300 hover:text-white transition-colors">Satış Ekipleri</Link></li>
                <li><Link to="/okullar" className="text-gray-300 hover:text-white transition-colors">Okullar</Link></li>
                <li><Link to="/e-ticaret" className="text-gray-300 hover:text-white transition-colors">E-Ticaret Siteleri</Link></li>
                <li><Link to="/pazarlama-uzmanlari" className="text-gray-300 hover:text-white transition-colors">Pazarlama Uzmanları</Link></li>
                <li><Link to="/reklam-ajanslari" className="text-gray-300 hover:text-white transition-colors">Reklam Ajansları</Link></li>
                <li><Link to="/sosyal-medya-ajanslari" className="text-gray-300 hover:text-white transition-colors">Sosyal Medya Ajansları</Link></li>
                <li><Link to="/organizasyon-firmalari" className="text-gray-300 hover:text-white transition-colors">Organizasyon Firmaları</Link></li>
                <li><Link to="/topluluk-yoneticileri" className="text-gray-300 hover:text-white transition-colors">Topluluk Yöneticileri</Link></li>
                <li><Link to="/musteri-hizmetleri" className="text-gray-300 hover:text-white transition-colors">Müşteri Hizmetleri</Link></li>
              </ul>
            </div>

            {/* Contact & Pricing */}
            <div className="lg:col-span-1">
              <div className="mb-8">
                <h3 className="text-xl font-bold mb-4">Fiyatlandırma</h3>
                <ul className="space-y-2">
                  <li><Link to="/ucretlendirme" className="text-gray-300 hover:text-white transition-colors">Whatsapp Mesaj Paketleri</Link></li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-4">İletişim</h3>
                <ul className="space-y-2">
                  <li><Link to="/iletisim" className="text-gray-300 hover:text-white transition-colors">İletişim</Link></li>
                  <li><a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors"><EMAIL></a></li>
                  <li><a href="tel:+905417173962" className="text-gray-300 hover:text-white transition-colors">0(212) 963 46 44</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t text-white border-gray-800 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-400 hover:text-white text-sm">
                WaChatify bir NocyTech projesidir.
              </p>
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/kvkk" className="text-gray-400 hover:text-white text-sm transition-colors">
                KVKK ve Gizlilik Sözleşmesi
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
                Hizmet Sözleşmesi
              </Link>
            </div>
          </div>
          <div className="mt-4 text-gray-400 hover:text-white text-center">
            <p className="text-sm">
              © 2024 WaChatify, Tüm hakları saklıdır.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
