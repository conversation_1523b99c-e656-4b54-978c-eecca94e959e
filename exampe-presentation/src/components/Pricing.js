import React from 'react';

const Pricing = () => {
  // Tanışma Paketleri
  const introPackages = [
    {
      name: '<PERSON> Tanışma',
      price: '299',
      originalPrice: '369',
      period: '',
      amount: '2,500',
      unit: 'mesaj',
      features: [
        '2,500 Mesaj Kredisi',
        'İlk alıma özel indirim',
        'Temel raporlama',
        'E-posta desteği'
      ],
      popular: true,
      buttonText: 'Satın Al',
      buttonClass: 'bg-green-600 hover:bg-green-700',
      discount: '19%'
    },
    {
      name: 'Super Tanışma',
      price: '569',
      originalPrice: '649',
      period: '',
      amount: '10,000',
      unit: 'mesaj',
      features: [
        '10,000 Mesaj Kredisi',
        'İlk alıma özel indirim',
        'Gelişmiş raporlama',
        'Ö<PERSON><PERSON>li destek'
      ],
      popular: false,
      buttonText: 'Satın Al',
      buttonClass: 'bg-blue-600 hover:bg-blue-700',
      discount: '12%'
    },
    {
      name: '<PERSON> Tanışma',
      price: '3,999',
      originalPrice: '4,949',
      period: '',
      amount: '100,000',
      unit: 'mesaj',
      features: [
        '100,000 Mesaj Kredisi',
        'İlk alıma özel indirim',
        'Premium raporlama',
        '7/24 destek'
      ],
      popular: false,
      buttonText: 'Satın Al',
      buttonClass: 'bg-purple-600 hover:bg-purple-700',
      discount: '19%'
    }
  ];

  // Normal Mesaj Paketleri
  const messagePackages = [
    {
      name: 'Mini',
      price: '119',
      period: '',
      amount: '500',
      unit: 'mesaj',
      features: [
        '500 Mesaj Kredisi',
        'Temel raporlama',
        'E-posta desteği',
        'Rehber aktarımı'
      ],
      popular: false,
      buttonText: 'Satın Al',
      buttonClass: 'bg-gray-600 hover:bg-gray-700'
    },
    {
      name: 'Eko',
      price: '369',
      period: '',
      amount: '2,500',
      unit: 'mesaj',
      features: [
        '2,500 Mesaj Kredisi',
        'Gelişmiş raporlama',
        'Öncelikli destek',
        'Zamanlı gönderim'
      ],
      popular: true,
      buttonText: 'En Popüler',
      buttonClass: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      name: 'Pro',
      price: '649',
      period: '',
      amount: '10,000',
      unit: 'mesaj',
      features: [
        '10,000 Mesaj Kredisi',
        'Premium raporlama',
        '7/24 destek',
        'API entegrasyonu'
      ],
      popular: false,
      buttonText: 'Satın Al',
      buttonClass: 'bg-purple-600 hover:bg-purple-700'
    }
  ];

  // Cihaz Paketleri
  const devicePackages = [
    {
      name: 'Ekstra Cihaz',
      price: '99',
      period: '/ay',
      amount: '1',
      unit: 'cihaz',
      features: [
        '1 Ekstra WhatsApp Cihazı',
        'Aylık abonelik',
        'Tüm özellikler dahil',
        'İstediğiniz zaman iptal'
      ],
      popular: false,
      buttonText: 'Satın Al',
      buttonClass: 'bg-green-600 hover:bg-green-700'
    },
    {
      name: 'Ekstra Cihaz',
      price: '999',
      period: '/yıl',
      amount: '1',
      unit: 'cihaz',
      features: [
        '1 Ekstra WhatsApp Cihazı',
        'Yıllık abonelik',
        '2 ay bedava',
        'Tüm özellikler dahil'
      ],
      popular: true,
      buttonText: 'En Avantajlı',
      buttonClass: 'bg-blue-600 hover:bg-blue-700'
    }
  ];

  // Özellik Paketleri
  const featurePackages = [
    {
      name: 'Cancel Link',
      price: '79',
      period: '/ay',
      amount: 'Cancel Link',
      unit: 'özellik',
      features: [
        'Mesaj iptal linki',
        'Aylık abonelik',
        'Gelişmiş kontrol',
        'İstediğiniz zaman iptal'
      ],
      popular: false,
      buttonText: 'Satın Al',
      buttonClass: 'bg-orange-600 hover:bg-orange-700'
    },
    {
      name: 'Cancel Link',
      price: '799',
      period: '/yıl',
      amount: 'Cancel Link',
      unit: 'özellik',
      features: [
        'Mesaj iptal linki',
        'Yıllık abonelik',
        '2 ay bedava',
        'Gelişmiş kontrol'
      ],
      popular: true,
      buttonText: 'En Avantajlı',
      buttonClass: 'bg-blue-600 hover:bg-blue-700'
    }
  ];

  const renderPackageCard = (pkg, index) => (
    <div
      key={index}
      className={`relative bg-white border-2 rounded-3xl transition-all duration-500 hover:-translate-y-2 ${
        pkg.popular
          ? 'border-emerald-500 shadow-2xl shadow-emerald-500/20 scale-105'
          : 'border-gray-200 hover:border-emerald-300 hover:shadow-xl'
      }`}
    >
      {/* Popular Badge */}
      {pkg.popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
            ⭐ En Popüler
          </span>
        </div>
      )}

      {/* Discount Badge */}
      {pkg.discount && (
        <div className="absolute -top-3 -right-3">
          <span className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
            {pkg.discount} İndirim
          </span>
        </div>
      )}

      <div className="p-8">
        {/* Package Name */}
        <h3 className="text-2xl font-black text-gray-900 mb-6 text-center">
          {pkg.name}
        </h3>

        {/* Price */}
        <div className="text-center mb-8">
          {pkg.originalPrice && (
            <div className="text-lg text-gray-400 line-through mb-2">
              {pkg.originalPrice} TL
            </div>
          )}
          <div className="text-5xl font-black text-gray-900 mb-2">
            {pkg.price}
            <span className="text-lg font-normal text-gray-600 ml-1">TL{pkg.period}</span>
          </div>
          <div className="inline-block bg-emerald-50 text-emerald-700 px-4 py-2 rounded-full text-sm font-semibold">
            {pkg.amount} {pkg.unit}
          </div>
        </div>

        {/* Features */}
        <ul className="space-y-4 mb-8">
          {pkg.features.map((feature, idx) => (
            <li key={idx} className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-gray-700 leading-relaxed">{feature}</span>
            </li>
          ))}
        </ul>

        {/* Button */}
        <button className={`w-full py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 ${
          pkg.popular
            ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700 shadow-lg hover:shadow-emerald-500/25'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}>
          {pkg.buttonText}
        </button>
      </div>
    </div>
  );

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block bg-emerald-100 rounded-full px-6 py-2 mb-6">
            <span className="text-emerald-700 font-semibold text-sm">FİYATLANDIRMA</span>
          </div>
          <h2 className="text-4xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight">
            Şeffaf Fiyatlar,
            <span className="block text-emerald-600">Sınırsız Değer</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            İhtiyacınıza uygun paketi seçin ve WhatsApp pazarlama gücünü keşfedin.
            Tüm paketlerde 7 gün ücretsiz deneme imkanı.
          </p>
        </div>

        {/* Tanışma Paketleri */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Tanışma Paketleri
            </h3>
            <p className="text-lg text-gray-600">
              İlk alıma özel indirimli fiyatlarla başlayın
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {introPackages.map((pkg, index) => renderPackageCard(pkg, `intro-${index}`))}
          </div>
        </div>

        {/* Normal Mesaj Paketleri */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Mesaj Paketleri
            </h3>
            <p className="text-lg text-gray-600">
              WhatsApp mesaj kredilerinizi satın alın
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {messagePackages.map((pkg, index) => renderPackageCard(pkg, `message-${index}`))}
          </div>
        </div>

        {/* Cihaz Paketleri */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Cihaz Paketleri
            </h3>
            <p className="text-lg text-gray-600">
              Ekstra WhatsApp cihazları bağlayın
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {devicePackages.map((pkg, index) => renderPackageCard(pkg, `device-${index}`))}
          </div>
        </div>

        {/* Özellik Paketleri */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Özellik Paketleri
            </h3>
            <p className="text-lg text-gray-600">
              Gelişmiş özellikleri aktifleştirin
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {featurePackages.map((pkg, index) => renderPackageCard(pkg, `feature-${index}`))}
          </div>
        </div>

        {/* Enterprise CTA */}
        <div className="mt-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">
            Özel İhtiyaçlarınız mı Var?
          </h3>
          <p className="text-lg mb-6 opacity-90">
            Kurumsal çözümler ve özel fiyatlandırma için bizimle iletişime geçin.
          </p>
          <a
            href="https://wa.me/905417173986"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            WhatsApp ile İletişime Geç
          </a>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
