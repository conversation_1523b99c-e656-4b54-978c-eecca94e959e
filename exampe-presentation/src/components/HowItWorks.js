import React from 'react';

const HowItWorks = () => {
  const steps = [
    {
      step: '1',
      title: '<PERSON><PERSON><PERSON>',
      description: 'H<PERSON>zlı ve kolay kayıt işlemi ile hesabınızı oluşturun',
      icon: '👤'
    },
    {
      step: '2',
      title: 'WhatsApp Bağlayın',
      description: 'QR kod ile WhatsApp hesabınızı güvenli şekilde bağlayın',
      icon: '📱'
    },
    {
      step: '3',
      title: '<PERSON><PERSON><PERSON>',
      description: 'Mevcut rehberinizi kolayca sisteme aktarın',
      icon: '📋'
    },
    {
      step: '4',
      title: '<PERSON><PERSON>',
      description: '<PERSON>lu mesajlarınızı hızlı ve etkili şekilde gönderin',
      icon: '🚀'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Nasıl Çalışır?
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            WaChatify ile WhatsApp toplu mesaj gönderimi sadece 4 adımda tamamlanır. 
            Basit ve kullanıcı dostu arayüzümüz ile dakikalar içinde başlayabilirsiniz.
          </p>
        </div>

        {/* Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="text-center relative">
              {/* Step Number */}
              <div className="relative mb-6">
                <div className="w-20 h-20 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4 shadow-lg">
                  {step.step}
                </div>
                <div className="text-4xl">{step.icon}</div>
                
                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-10 left-1/2 w-full h-0.5 bg-gray-300 transform translate-x-10">
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                      <svg className="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>

              {/* Step Content */}
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {step.title}
              </h3>
              <p className="text-gray-600">
                {step.description}
              </p>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-20 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                WhatsApp Yeşil Tik Doğrulaması
              </h3>
              <p className="text-gray-600 mb-6">
                Bu makalede, WhatsApp'ta görünen adınızın yanında yeşil tik işaretini nasıl 
                alacağınızı keşfedecek ve bunun işletmenize getirebileceği avantajları 
                anlayacaksınız. WhatsApp yeşil tik işaretinin ne olduğunu anlayalım ve buna 
                ihtiyacınız olup olmadığına karar verelim.
              </p>
              <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                Devamını Oku
              </button>
            </div>
            <div className="flex justify-center">
              <div className="bg-white rounded-lg p-6 shadow-lg">
                <div className="w-32 h-32 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-16 h-16 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-center mt-4 font-semibold text-gray-900">Doğrulanmış İşletme</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
