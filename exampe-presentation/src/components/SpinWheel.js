import React, { useState } from 'react';

const SpinWheel = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState('wheel'); // wheel, result, form, end
  const [prize, setPrize] = useState('');
  const [isSpinning, setIsSpinning] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    phoneNumber: '',
    businessName: ''
  });

  const prizes = [
    "75 TL İndirim",
    "100 TL İndirim", 
    "250 WhatsApp Kredisi",
    "50 TL İndirim",
    "1000 WhatsApp Kredisi",
    "25 TL İndirim",
    "750 WhatsApp Kredisi",
    "500 WhatsApp Kredisi"
  ];

  const handleSpin = () => {
    if (isSpinning) return;
    
    setIsSpinning(true);
    const randomIndex = Math.floor(Math.random() * prizes.length);
    const selectedPrize = prizes[randomIndex];
    
    // Simulate spinning animation
    setTimeout(() => {
      setPrize(selectedPrize);
      setCurrentStep('result');
      setIsSpinning(false);
    }, 3000);
  };

  const handleClaimPrize = () => {
    setCurrentStep('form');
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name || !formData.phoneNumber) {
      alert('Lütfen gerekli alanları doldurun.');
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCurrentStep('end');
    } catch (error) {
      alert('Bir hata oluştu. Lütfen tekrar deneyiniz.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
        {/* Close Button */}
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl z-10"
        >
          ×
        </button>

        {/* Wheel Step */}
        {currentStep === 'wheel' && (
          <div className="p-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Bugün Çok Şanslısınız!!!!
            </h2>
            <p className="text-lg text-gray-600 mb-6">
              Hediyenizi kazanmak için Ödül çarkını hemen çevirin.
            </p>
            
            <div className="mb-6">
              <ul className="text-sm text-gray-600 space-y-1">
                <li>- Çarkı yalnızca bir kez çevirebilirsiniz</li>
                <li>- Uzman ekibimizden en uygun teklifi alın</li>
                <li>- Hediye çarkı yeni abonelerimize özeldir</li>
                <li>- Kazanılan hediye 2.500 ve üzeri WhatsApp paketi alımlarında yüklenir.</li>
              </ul>
            </div>

            {/* Wheel */}
            <div className="relative mx-auto mb-8" style={{ width: '300px', height: '300px' }}>
              <div 
                className={`w-full h-full rounded-full border-8 border-gray-300 relative transition-transform duration-3000 ${
                  isSpinning ? 'animate-spin' : ''
                }`}
                style={{
                  background: `conic-gradient(
                    #ff6b6b 0deg 45deg,
                    #4ecdc4 45deg 90deg,
                    #45b7d1 90deg 135deg,
                    #96ceb4 135deg 180deg,
                    #ffeaa7 180deg 225deg,
                    #dda0dd 225deg 270deg,
                    #98d8c8 270deg 315deg,
                    #f7dc6f 315deg 360deg
                  )`
                }}
              >
                {/* Pointer */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                  <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
                </div>
                
                {/* Prize Labels */}
                {prizes.map((prize, index) => (
                  <div
                    key={index}
                    className="absolute text-xs font-bold text-white"
                    style={{
                      top: '50%',
                      left: '50%',
                      transform: `translate(-50%, -50%) rotate(${index * 45}deg) translateY(-100px)`,
                      transformOrigin: 'center'
                    }}
                  >
                    <span style={{ transform: `rotate(-${index * 45}deg)`, display: 'block' }}>
                      {prize}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <button
              onClick={handleSpin}
              disabled={isSpinning}
              className="bg-green-500 text-white px-8 py-3 rounded-full text-lg font-semibold hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSpinning ? 'Çeviriyor...' : 'Çevir & Kazan'}
            </button>
          </div>
        )}

        {/* Result Step */}
        {currentStep === 'result' && (
          <div className="p-8 text-center">
            <h2 className="text-3xl font-bold text-green-600 mb-4">
              Tebrikler!
            </h2>
            <h3 className="text-2xl font-semibold text-gray-900 mb-6">
              Ödül Kazandınız
            </h3>
            
            <div className="bg-green-100 rounded-lg p-6 mb-6">
              <div className="text-4xl mb-4">🎉</div>
              <div className="text-xl font-bold text-green-700">{prize}</div>
            </div>

            <p className="text-gray-600 mb-6">
              Şimdi kullanın - Ücretsiz
            </p>

            <button
              onClick={handleClaimPrize}
              className="bg-green-500 text-white px-8 py-3 rounded-full text-lg font-semibold hover:bg-green-600 transition-colors"
            >
              Hediyeni Al
            </button>
          </div>
        )}

        {/* Form Step */}
        {currentStep === 'form' && (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2 text-center">
              Tebrikler!
            </h2>
            <h3 className="text-xl font-semibold text-gray-700 mb-6 text-center">
              Lütfen İletişim Bilgilerinizi Giriniz:
            </h3>

            <div className="bg-green-100 rounded-lg p-4 mb-6 text-center">
              <div className="text-lg font-bold text-green-700">Kazandığınız Ödül: {prize}</div>
            </div>

            <form onSubmit={handleSubmit} className="max-w-md mx-auto space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Adınız: *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Telefon Numaranız: *
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-2">
                  İşletme Adınız:
                </label>
                <input
                  type="text"
                  id="businessName"
                  name="businessName"
                  value={formData.businessName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              <button
                type="submit"
                className="w-full bg-green-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-600 transition-colors"
              >
                Hediyeni Al
              </button>
            </form>
          </div>
        )}

        {/* End Step */}
        {currentStep === 'end' && (
          <div className="p-8 text-center">
            <h2 className="text-3xl font-bold text-green-600 mb-4">
              Tebrikler!
            </h2>
            
            <div className="text-4xl mb-6">🎉</div>
            
            <p className="text-lg text-gray-600 mb-6">
              Ödül talebiniz tarafımıza ulaştı. Tanımlama ve sizleri bilgilendirme amaçlı, 
              mesai saatlerimiz içerisinde (09:00-18:00) destek uzmanımız sizinle iletişime geçecek.
            </p>

            <button
              onClick={onClose}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Tamam
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SpinWheel;
