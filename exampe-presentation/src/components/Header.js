import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [language, setLanguage] = useState('tr');

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleLanguage = () => {
    setLanguage(language === 'tr' ? 'en' : 'tr');
  };

  return (
    <>
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-3">
        <div className="container mx-auto px-6 flex justify-between items-center">
          <div className="text-sm font-medium">
            <span className="inline-flex items-center">
              🎉 <span className="ml-2 font-bold">Özel Fırsat!</span>
              <span className="ml-2">7 gün ücretsiz deneme + %25 indirim</span>
            </span>
          </div>
          <div className="flex items-center space-x-6">
            <Link to="/sss" className="text-sm hover:text-emerald-200 transition-colors font-medium">
              S.S.S
            </Link>
            <button
              onClick={toggleLanguage}
              className="text-sm bg-white/20 backdrop-blur-sm border border-white/30 px-3 py-1 rounded-full hover:bg-white hover:text-emerald-600 transition-all duration-300 font-medium"
            >
              {language === 'tr' ? '🇹🇷 TR' : '🇺🇸 EN'}
            </button>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-white/95 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b border-gray-100">
        <div className="container mx-auto px-6">
          <div className="flex justify-between items-center py-5">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="text-3xl font-black bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                WaChatify
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              <Link to="/nedir" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                Nedir ?
              </Link>
              <Link to="/nasil-calisir" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                Nasıl Çalışır ?
              </Link>
              <Link to="/ucretlendirme" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                Ücretlendirme
              </Link>
              <Link to="/iletisim" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                İletişim
              </Link>
              <Link to="/sss" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                SSS
              </Link>
            </nav>

            {/* Desktop Action Buttons */}
            <div className="hidden lg:flex items-center space-x-4">
              <a
                href="https://app.wachatify.com/login"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
              >
                Giriş Yap
              </a>
              <a
                href="https://app.wachatify.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-3 rounded-full hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 font-bold shadow-lg hover:shadow-emerald-500/25"
              >
                Kayıt Ol
              </a>
            </div>

            {/* Mobile Menu Button */}
            <button 
              onClick={toggleMenu}
              className="lg:hidden text-gray-700 hover:text-blue-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden border-t border-gray-200 py-4">
              <nav className="space-y-4">
                <Link to="/nedir" className="block text-gray-700 hover:text-blue-600 transition-colors">
                  Nedir ?
                </Link>
                <Link to="/nasil-calisir" className="block text-gray-700 hover:text-blue-600 transition-colors">
                  Nasıl Çalışır ?
                </Link>
                <Link to="/ucretlendirme" className="block text-gray-700 hover:text-blue-600 transition-colors">
                  Ücretlendirme
                </Link>
                <Link to="/iletisim" className="block text-gray-700 hover:text-blue-600 transition-colors">
                  İletişim
                </Link>
                <Link to="/sss" className="block text-gray-700 hover:text-blue-600 transition-colors">
                  SSS
                </Link>
                <div className="pt-4 border-t border-gray-200 space-y-2">
                  <a
                    href="https://app.wachatify.com/login"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    Giriş Yap
                  </a>
                  <a
                    href="https://app.wachatify.com/register"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center"
                  >
                    Kayıt Ol
                  </a>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
};

export default Header;
