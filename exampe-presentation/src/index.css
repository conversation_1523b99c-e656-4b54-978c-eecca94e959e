@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  border-color: rgb(229 231 235);
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

body {
  background-color: #ffffff;
  color: #000000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

#root {
  height: 100%;
  width: 100%;
  min-height: 100vh;
}

/* Component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border: none;
}

.btn:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: rgb(37 99 235);
  color: white;
}

.btn-primary:hover {
  background-color: rgb(29 78 216);
}

.btn-secondary {
  background-color: rgb(229 231 235);
  color: rgb(17 24 39);
}

.btn-secondary:hover {
  background-color: rgb(209 213 219);
}

.btn-success {
  background-color: rgb(34 197 94);
  color: white;
}

.btn-success:hover {
  background-color: rgb(22 163 74);
}

.btn-danger {
  background-color: rgb(239 68 68);
  color: white;
}

.btn-danger:hover {
  background-color: rgb(220 38 38);
}

.card {
  border-radius: 0.5rem;
  border: 1px solid rgb(229 231 235);
  background-color: white;
  padding: 1.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid rgb(209 213 219);
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.input::placeholder {
  color: rgb(107 114 128);
}

.input:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(59 130 246);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Responsive utilities */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Mobile-first responsive text */
.text-responsive-sm {
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: 1rem;
  }
}

.text-responsive-base {
  font-size: 1rem;
}

@media (min-width: 768px) {
  .text-responsive-base {
    font-size: 1.125rem;
  }
}

.text-responsive-lg {
  font-size: 1.125rem;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 1.25rem;
  }
}

.text-responsive-xl {
  font-size: 1.25rem;
}

@media (min-width: 768px) {
  .text-responsive-xl {
    font-size: 1.5rem;
  }
}

.text-responsive-2xl {
  font-size: 1.5rem;
}

@media (min-width: 768px) {
  .text-responsive-2xl {
    font-size: 1.875rem;
  }
}

.text-responsive-3xl {
  font-size: 1.875rem;
}

@media (min-width: 768px) {
  .text-responsive-3xl {
    font-size: 2.25rem;
  }
}

/* Mobile spacing utilities */
.space-responsive-sm > * + * {
  margin-top: 0.5rem;
}

@media (min-width: 768px) {
  .space-responsive-sm > * + * {
    margin-top: 1rem;
  }
}

.space-responsive-base > * + * {
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .space-responsive-base > * + * {
    margin-top: 1.5rem;
  }
}

.space-responsive-lg > * + * {
  margin-top: 1.5rem;
}

@media (min-width: 768px) {
  .space-responsive-lg > * + * {
    margin-top: 2rem;
  }
}

/* Touch-friendly button sizing */
.btn-touch {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
}

@media (min-width: 768px) {
  .btn-touch {
    min-height: auto;
    min-width: auto;
    padding: 0.5rem 1rem;
  }
}

/* Prevent text selection on mobile for UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Safe area padding for mobile devices */
.safe-area-padding {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* WAMessage specific styles */
.mobile-image {
  display: none !important;
}

p {
  color: #000 !important;
}

.headtop {
  background-color: #066bb9;
}

.headtopright .lang select {
  background-color: #066bb9;
}

.sectionappbar h2 {
  color: #066bb9;
}

.tab-content-span {
  color: #000 !important;
}

.qr-code-container {
  background-color: #fff;
}

.sectionappbar .img {
  height: auto !important;
}

@media(max-width:950px) {
  .blogSwiper .swiper-slide-prev,
  .hmblogitem,
  .swiper,
  .blogSwiper .swiper-slide-next {
    overflow: inherit !important;
  }

  .desktop-blog {
    display: none;
  }
}

@media (max-width:768px) {
  .blogSwiper .swiper-slide-active .hmblogitem .text p {
    margin-bottom: 0 !important;
  }

  .blogSwiper .swiper-slide-active .hmblogitem .text .devamini-oku {
    display: block;
    margin-inline: auto;
    width: 100%;
    margin-top: 20px !important;
  }

  .qr-code-title {
    font-size: 24px !important;
    margin: 0 !important;
  }

  .qr-code-img {
    max-width: 200px !important;
  }

  .desktop-image {
    display: none !important;
  }

  .mobile-image {
    display: block !important;
  }

  .qr-code-container {
    background-color: #e4f4f5;
  }
}

/* Custom animations */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Notification styles */
#snackbar {
  visibility: hidden;
  min-width: 250px;
  margin-left: -125px;
  background-color: white;
  border: 1px solid red;
  color: red;
  border-radius: 5px;
  text-align: center;
  padding: 20px;
  position: fixed;
  z-index: 1;
  right: 30px;
  top: 135px;
  font-size: 17px;
}

#snackbar.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

#snackbarTrue {
  visibility: hidden;
  min-width: 250px;
  margin-left: -125px;
  background-color: white;
  border: 1px solid green;
  color: green;
  border-radius: 5px;
  text-align: center;
  padding: 20px;
  position: fixed;
  z-index: 1;
  right: 30px;
  top: 135px;
  font-size: 17px;
}

#snackbarTrue.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
  from {
    right: 0;
    opacity: 0;
  }
  to {
    right: 30px;
    opacity: 1;
  }
}

@keyframes fadein {
  from {
    right: 0;
    opacity: 0;
  }
  to {
    right: 30px;
    opacity: 1;
  }
}

@-webkit-keyframes fadeout {
  from {
    right: 30px;
    opacity: 1;
  }
  to {
    right: 0;
    opacity: 0;
  }
}

@keyframes fadeout {
  from {
    right: 30px;
    opacity: 1;
  }
  to {
    right: 0;
    opacity: 0;
  }
}

/* Spin wheel styles */
.duration-3000 {
  transition-duration: 3000ms;
}
