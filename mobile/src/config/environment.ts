import Constants from 'expo-constants';

export interface EnvironmentConfig {
  API_BASE_URL: string;
  API_TIMEOUT: number;
  DEBUG_MODE: boolean;
  APP_NAME: string;
  APP_VERSION: string;
}

// Environment detection
const isDevelopment = __DEV__;
const isProduction = !__DEV__;

// Get the release channel from environment variables or EAS config
const releaseChannel = process.env.NODE_ENV || 'development';

// Determine environment based on various factors
const getEnvironment = (): 'development' | 'staging' | 'production' => {
  if (isDevelopment) {
    return 'development';
  }
  
  if (releaseChannel === 'staging') {
    return 'staging';
  }
  
  return 'production';
};

const environment = getEnvironment();

// Environment-specific configurations
const environmentConfigs: Record<string, EnvironmentConfig> = {
  development: {
    API_BASE_URL: 'http://localhost:8008/api/v1',
    API_TIMEOUT: 10000,
    DEBUG_MODE: true,
    APP_NAME: 'Butce360 (Dev)',
    APP_VERSION: Constants.expoConfig?.version || '1.0.0',
  },
  staging: {
    API_BASE_URL: 'https://app.butce360.com/api/v1',
    API_TIMEOUT: 15000,
    DEBUG_MODE: true,
    APP_NAME: 'Butce360 (Staging)',
    APP_VERSION: Constants.expoConfig?.version || '1.0.0',
  },
  production: {
    API_BASE_URL: 'https://app.butce360.com/api/v1',
    API_TIMEOUT: 30000, // Increased timeout for production
    DEBUG_MODE: false,
    APP_NAME: 'Butce360',
    APP_VERSION: Constants.expoConfig?.version || '1.0.0',
  },
};

// Export the current environment configuration
export const config: EnvironmentConfig = environmentConfigs[environment];

// Export environment utilities
export const ENV = {
  isDevelopment,
  isProduction,
  isStaging: environment === 'staging',
  current: environment,
  releaseChannel,
};

// Debug logging utility
export const debugLog = (...args: any[]) => {
  if (config.DEBUG_MODE) {
    console.log('[DEBUG]', ...args);
  }
};

// API URL utilities
export const getApiUrl = (endpoint: string = ''): string => {
  const baseUrl = config.API_BASE_URL;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
};

// Network configuration
export const networkConfig = {
  timeout: config.API_TIMEOUT,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Feature flags based on environment
export const featureFlags = {
  enableLogging: config.DEBUG_MODE,
  enableCrashReporting: isProduction,
  enableAnalytics: isProduction,
  enableBetaFeatures: environment === 'staging' || isDevelopment,
  enableMockData: true, // API problemi olduğunda mock data kullan
};

// Export for debugging
if (config.DEBUG_MODE) {
  console.log('🌍 Environment Configuration:', {
    environment,
    config,
    ENV,
    featureFlags,
  });
}
