import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { theme as lightTheme } from '../theme';
import { darkColors, lightColors, ColorScheme } from '../theme/colors';

// Create dark theme
const darkTheme = {
  ...lightTheme,
  colors: darkColors,
};

export interface ThemeState {
  colorScheme: ColorScheme;
  isDark: boolean;
  theme: typeof lightTheme;
  isLoading: boolean;
}

export interface ThemeContextType {
  state: ThemeState;
  setColorScheme: (scheme: ColorScheme | 'auto') => void;
  toggleTheme: () => void;
}

type ThemeAction =
  | { type: 'SET_COLOR_SCHEME'; payload: ColorScheme }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'TOGGLE_THEME' };

const THEME_STORAGE_KEY = 'app_theme';

const getSystemColorScheme = (): ColorScheme => {
  const systemScheme = Appearance.getColorScheme();
  return systemScheme === 'dark' ? 'dark' : 'light';
};

const initialState: ThemeState = {
  colorScheme: getSystemColorScheme(),
  isDark: getSystemColorScheme() === 'dark',
  theme: getSystemColorScheme() === 'dark' ? darkTheme : lightTheme,
  isLoading: true,
};

const themeReducer = (state: ThemeState, action: ThemeAction): ThemeState => {
  switch (action.type) {
    case 'SET_COLOR_SCHEME':
      const isDark = action.payload === 'dark';
      return {
        ...state,
        colorScheme: action.payload,
        isDark,
        theme: isDark ? darkTheme : lightTheme,
        isLoading: false,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'TOGGLE_THEME':
      const newScheme: ColorScheme = state.isDark ? 'light' : 'dark';
      const newIsDark = newScheme === 'dark';
      return {
        ...state,
        colorScheme: newScheme,
        isDark: newIsDark,
        theme: newIsDark ? darkTheme : lightTheme,
      };
    default:
      return state;
  }
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState);

  useEffect(() => {
    loadThemePreference();
    
    // Listen to system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      // Only update if user hasn't set a manual preference
      checkAndUpdateSystemTheme();
    });

    return () => subscription?.remove();
  }, []);

  const loadThemePreference = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      
      if (savedTheme) {
        const themeData = JSON.parse(savedTheme);
        
        if (themeData.scheme === 'auto') {
          // Use system theme
          const systemScheme = getSystemColorScheme();
          dispatch({ type: 'SET_COLOR_SCHEME', payload: systemScheme });
        } else {
          // Use saved theme
          dispatch({ type: 'SET_COLOR_SCHEME', payload: themeData.scheme });
        }
      } else {
        // Default to system theme
        const systemScheme = getSystemColorScheme();
        dispatch({ type: 'SET_COLOR_SCHEME', payload: systemScheme });
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
      // Fallback to system theme
      const systemScheme = getSystemColorScheme();
      dispatch({ type: 'SET_COLOR_SCHEME', payload: systemScheme });
    }
  };

  const checkAndUpdateSystemTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      
      if (!savedTheme || JSON.parse(savedTheme).scheme === 'auto') {
        // Update to current system theme
        const systemScheme = getSystemColorScheme();
        dispatch({ type: 'SET_COLOR_SCHEME', payload: systemScheme });
      }
    } catch (error) {
      console.error('Error checking system theme:', error);
    }
  };

  const setColorScheme = async (scheme: ColorScheme | 'auto') => {
    try {
      if (scheme === 'auto') {
        // Use system theme
        const systemScheme = getSystemColorScheme();
        dispatch({ type: 'SET_COLOR_SCHEME', payload: systemScheme });
        
        // Save preference
        await AsyncStorage.setItem(
          THEME_STORAGE_KEY,
          JSON.stringify({ scheme: 'auto' })
        );
      } else {
        // Use manual theme
        dispatch({ type: 'SET_COLOR_SCHEME', payload: scheme });
        
        // Save preference
        await AsyncStorage.setItem(
          THEME_STORAGE_KEY,
          JSON.stringify({ scheme })
        );
      }
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = async () => {
    try {
      dispatch({ type: 'TOGGLE_THEME' });
      
      const newScheme: ColorScheme = state.isDark ? 'light' : 'dark';
      
      // Save preference
      await AsyncStorage.setItem(
        THEME_STORAGE_KEY,
        JSON.stringify({ scheme: newScheme })
      );
    } catch (error) {
      console.error('Error toggling theme:', error);
    }
  };

  const value: ThemeContextType = {
    state,
    setColorScheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Utility hooks
export const useColors = () => {
  const { state } = useTheme();
  return state.theme.colors;
};

export const useTypography = () => {
  const { state } = useTheme();
  return state.theme.typography;
};

export const useSpacing = () => {
  const { state } = useTheme();
  return state.theme.spacing;
};

export const useShadows = () => {
  const { state } = useTheme();
  return state.theme.shadows;
};

export const useIsDark = () => {
  const { state } = useTheme();
  return state.isDark;
};
