import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Budget, BudgetInput, BudgetService } from '../services/BudgetService';
import { useAuth } from './AuthProvider';
import { mockBudgets } from '../data/mockData';

interface BudgetState {
  budgets: Budget[];
  loading: boolean;
  error: string | null;
}

type BudgetAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_BUDGETS'; payload: Budget[] }
  | { type: 'ADD_BUDGET'; payload: Budget }
  | { type: 'UPDATE_BUDGET'; payload: Budget }
  | { type: 'DELETE_BUDGET'; payload: string };

const initialState: BudgetState = {
  budgets: [],
  loading: false,
  error: null,
};

const budgetReducer = (state: BudgetState, action: BudgetAction): BudgetState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_BUDGETS':
      return { ...state, budgets: action.payload, loading: false, error: null };
    case 'ADD_BUDGET':
      return { ...state, budgets: [...state.budgets, action.payload] };
    case 'UPDATE_BUDGET':
      return {
        ...state,
        budgets: state.budgets.map(b =>
          b.id === action.payload.id ? action.payload : b
        ),
      };
    case 'DELETE_BUDGET':
      return {
        ...state,
        budgets: state.budgets.filter(b => b.id !== action.payload),
      };
    default:
      return state;
  }
};

interface BudgetContextType {
  state: BudgetState;
  loadBudgets: () => Promise<void>;
  createBudget: (input: BudgetInput) => Promise<Budget>;
  updateBudget: (id: string, updates: Partial<BudgetInput>) => Promise<Budget>;
  deleteBudget: (id: string) => Promise<void>;
  getBudgetById: (id: string) => Budget | undefined;
  getBudgetsByCategory: (categoryId: string) => Budget[];
  getBudgetsByPeriod: (period: 'monthly' | 'yearly') => Budget[];
  getBudgetSummary: () => Promise<{
    totalBudget: number;
    totalSpent: number;
    totalRemaining: number;
    budgetCount: number;
    overBudgetCount: number;
  }>;
  calculateBudgetProgress: (budget: Budget) => {
    percentage: number;
    status: 'safe' | 'warning' | 'danger';
    isOverBudget: boolean;
  };
  getBudgetColor: (budget: Budget) => string;
}

const BudgetContext = createContext<BudgetContextType | undefined>(undefined);

export const useBudgets = () => {
  const context = useContext(BudgetContext);
  if (!context) {
    throw new Error('useBudgets must be used within a BudgetProvider');
  }
  return context;
};

interface BudgetProviderProps {
  children: React.ReactNode;
}

export const BudgetProvider: React.FC<BudgetProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(budgetReducer, initialState);
  const budgetService = new BudgetService();
  const { state: authState } = useAuth();

  // Reload data when auth state changes (guest -> authenticated)
  useEffect(() => {
    if (authState.isAuthenticated && !authState.isGuest) {
      loadBudgets();
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  const loadBudgets = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      if (authState.isGuest) {
        // Use mock data for guest mode
        dispatch({ type: 'SET_BUDGETS', payload: mockBudgets });
      } else {
        // Use real API for authenticated users
        const budgets = await budgetService.getBudgets();
        dispatch({ type: 'SET_BUDGETS', payload: budgets });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [authState.isGuest]);

  const createBudget = useCallback(async (input: BudgetInput): Promise<Budget> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda bütçe ekleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const budget = await budgetService.createBudget(input);
      dispatch({ type: 'ADD_BUDGET', payload: budget });
      return budget;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const updateBudget = useCallback(async (
    id: string,
    updates: Partial<BudgetInput>
  ): Promise<Budget> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda bütçe düzenleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const budget = await budgetService.updateBudget(id, updates);
      dispatch({ type: 'UPDATE_BUDGET', payload: budget });
      return budget;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const deleteBudget = useCallback(async (id: string): Promise<void> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda bütçe silemezsiniz. Lütfen giriş yapın.');
    }

    try {
      await budgetService.deleteBudget(id);
      dispatch({ type: 'DELETE_BUDGET', payload: id });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const getBudgetById = useCallback((id: string): Budget | undefined => {
    return state.budgets.find(b => b.id === id);
  }, [state.budgets]);

  const getBudgetsByCategory = useCallback((categoryId: string): Budget[] => {
    return state.budgets.filter(b => b.categoryId === categoryId);
  }, [state.budgets]);

  const getBudgetsByPeriod = useCallback((period: 'monthly' | 'yearly'): Budget[] => {
    return state.budgets.filter(b => b.period === period);
  }, [state.budgets]);

  const getBudgetSummary = useCallback(async () => {
    return await budgetService.getBudgetSummary();
  }, []);

  const calculateBudgetProgress = useCallback((budget: Budget) => {
    return budgetService.calculateBudgetProgress(budget);
  }, []);

  const getBudgetColor = useCallback((budget: Budget): string => {
    return budgetService.getBudgetColor(budget);
  }, []);

  const value: BudgetContextType = {
    state,
    loadBudgets,
    createBudget,
    updateBudget,
    deleteBudget,
    getBudgetById,
    getBudgetsByCategory,
    getBudgetsByPeriod,
    getBudgetSummary,
    calculateBudgetProgress,
    getBudgetColor,
  };

  return (
    <BudgetContext.Provider value={value}>
      {children}
    </BudgetContext.Provider>
  );
};
