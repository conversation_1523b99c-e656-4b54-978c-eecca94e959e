import React from 'react';
import { AuthProvider } from './AuthProvider';
import { TransactionProvider } from './TransactionProvider';
import { CategoryProvider } from './CategoryProvider';
import { AccountProvider } from './AccountProvider';
import { BudgetProvider } from './BudgetProvider';
import { ThemeProvider } from './ThemeProvider';

interface AppProvidersProps {
  children: React.ReactNode;
}

export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ThemeProvider>
      <AuthProvider>
        <CategoryProvider>
          <AccountProvider>
            <BudgetProvider>
              <TransactionProvider>
                {children}
              </TransactionProvider>
            </BudgetProvider>
          </AccountProvider>
        </CategoryProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export * from './AuthProvider';
export * from './TransactionProvider';
export * from './CategoryProvider';
export * from './AccountProvider';
export * from './BudgetProvider';
export * from './ThemeProvider';
