import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Account, AccountInput } from '../models';
import { AccountService } from '../services';
import { useAuth } from './AuthProvider';
import { mockAccounts } from '../data/mockData';

interface AccountState {
  accounts: Account[];
  loading: boolean;
  error: string | null;
}

type AccountAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ACCOUNTS'; payload: Account[] }
  | { type: 'ADD_ACCOUNT'; payload: Account }
  | { type: 'UPDATE_ACCOUNT'; payload: Account }
  | { type: 'DELETE_ACCOUNT'; payload: string };

const initialState: AccountState = {
  accounts: [],
  loading: false,
  error: null,
};

const accountReducer = (state: AccountState, action: AccountAction): AccountState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_ACCOUNTS':
      return { ...state, accounts: action.payload, loading: false, error: null };
    case 'ADD_ACCOUNT':
      return {
        ...state,
        accounts: [...(state.accounts || []), action.payload]
      };
    case 'UPDATE_ACCOUNT':
      return {
        ...state,
        accounts: (state.accounts || []).map(a =>
          a.id === action.payload.id ? action.payload : a
        ),
      };
    case 'DELETE_ACCOUNT':
      return {
        ...state,
        accounts: (state.accounts || []).filter(a => a.id !== action.payload),
      };
    default:
      return state;
  }
};

interface AccountContextType {
  state: AccountState;
  loadAccounts: () => Promise<void>;
  createAccount: (input: AccountInput) => Promise<Account>;
  updateAccount: (id: string, updates: Partial<AccountInput>) => Promise<Account>;
  deleteAccount: (id: string) => Promise<void>;
  getAccountById: (id: string) => Account | undefined;
  getDefaultAccount: () => Account | undefined;
  getTotalBalance: () => number;
  initializeDefaultAccounts: () => Promise<void>;
}

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export const useAccounts = () => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccounts must be used within an AccountProvider');
  }
  return context;
};

interface AccountProviderProps {
  children: React.ReactNode;
}

export const AccountProvider: React.FC<AccountProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(accountReducer, initialState);
  const { state: authState } = useAuth();
  const accountService = new AccountService(authState.isGuest);

  // Update service guest mode and reload data when auth state changes
  useEffect(() => {
    accountService.setGuestMode(authState.isGuest);
    loadAccounts();
  }, [authState.isAuthenticated, authState.isGuest]);

  const loadAccounts = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      if (authState.isGuest) {
        // Use mock data for guest mode
        dispatch({ type: 'SET_ACCOUNTS', payload: mockAccounts });
      } else {
        // Use real API for authenticated users
        const accounts = await accountService.getAllAccounts();
        dispatch({ type: 'SET_ACCOUNTS', payload: accounts });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [authState.isGuest]);

  const createAccount = useCallback(async (input: AccountInput): Promise<Account> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda hesap ekleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const account = await accountService.createAccount(input);
      dispatch({ type: 'ADD_ACCOUNT', payload: account });
      return account;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const updateAccount = useCallback(async (
    id: string,
    updates: Partial<AccountInput>
  ): Promise<Account> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda hesap düzenleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const account = await accountService.updateAccount(id, updates);
      dispatch({ type: 'UPDATE_ACCOUNT', payload: account });
      return account;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const deleteAccount = useCallback(async (id: string): Promise<void> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda hesap silemezsiniz. Lütfen giriş yapın.');
    }

    try {
      await accountService.deleteAccount(id);
      dispatch({ type: 'DELETE_ACCOUNT', payload: id });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const getAccountById = useCallback((id: string): Account | undefined => {
    if (!state.accounts || !Array.isArray(state.accounts)) {
      return undefined;
    }
    return state.accounts.find(a => a.id === id);
  }, [state.accounts]);

  const getDefaultAccount = useCallback((): Account | undefined => {
    if (!state.accounts || !Array.isArray(state.accounts)) {
      return undefined;
    }
    return state.accounts.find(a => a.isDefault);
  }, [state.accounts]);

  const getTotalBalance = useCallback((): number => {
    if (!state.accounts || !Array.isArray(state.accounts)) {
      return 0;
    }
    return state.accounts.reduce((total, account) => total + account.balance, 0);
  }, [state.accounts]);

  const initializeDefaultAccounts = useCallback(async (): Promise<void> => {
    try {
      await accountService.initializeDefaultAccounts();
      await loadAccounts();
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [loadAccounts]);

  const value: AccountContextType = {
    state,
    loadAccounts,
    createAccount,
    updateAccount,
    deleteAccount,
    getAccountById,
    getDefaultAccount,
    getTotalBalance,
    initializeDefaultAccounts,
  };

  return (
    <AccountContext.Provider value={value}>
      {children}
    </AccountContext.Provider>
  );
};
