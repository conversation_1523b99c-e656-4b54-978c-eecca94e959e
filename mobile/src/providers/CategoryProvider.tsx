import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Category, CategoryInput } from '../models';
import { CategoryService } from '../services';
import { useAuth } from './AuthProvider';
import { mockCategories } from '../data/mockData';

interface CategoryState {
  categories: Category[];
  loading: boolean;
  error: string | null;
}

type CategoryAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'ADD_CATEGORY'; payload: Category }
  | { type: 'UPDATE_CATEGORY'; payload: Category }
  | { type: 'DELETE_CATEGORY'; payload: string };

const initialState: CategoryState = {
  categories: [],
  loading: false,
  error: null,
};

const categoryReducer = (state: CategoryState, action: CategoryAction): CategoryState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_CATEGORIES':
      return { ...state, categories: action.payload, loading: false, error: null };
    case 'ADD_CATEGORY':
      return { ...state, categories: [...state.categories, action.payload] };
    case 'UPDATE_CATEGORY':
      return {
        ...state,
        categories: state.categories.map(c =>
          c.id === action.payload.id ? action.payload : c
        ),
      };
    case 'DELETE_CATEGORY':
      return {
        ...state,
        categories: state.categories.filter(c => c.id !== action.payload),
      };
    default:
      return state;
  }
};

interface CategoryContextType {
  state: CategoryState;
  loadCategories: (type?: 'income' | 'expense') => Promise<void>;
  createCategory: (input: CategoryInput) => Promise<Category>;
  updateCategory: (id: string, updates: Partial<CategoryInput>) => Promise<Category>;
  deleteCategory: (id: string) => Promise<void>;
  getCategoryById: (id: string) => Category | undefined;
  getIncomeCategories: () => Category[];
  getExpenseCategories: () => Category[];
  initializeDefaultCategories: () => Promise<void>;
}

const CategoryContext = createContext<CategoryContextType | undefined>(undefined);

export const useCategories = () => {
  const context = useContext(CategoryContext);
  if (!context) {
    throw new Error('useCategories must be used within a CategoryProvider');
  }
  return context;
};

interface CategoryProviderProps {
  children: React.ReactNode;
}

export const CategoryProvider: React.FC<CategoryProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(categoryReducer, initialState);
  const { state: authState } = useAuth();
  const categoryService = new CategoryService(authState.isGuest);

  // Update service guest mode and reload data when auth state changes
  useEffect(() => {
    categoryService.setGuestMode(authState.isGuest);
    loadCategories();
  }, [authState.isAuthenticated, authState.isGuest]);

  const loadCategories = useCallback(async (type?: 'income' | 'expense') => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      if (authState.isGuest) {
        // Use mock data for guest mode
        let categories = mockCategories;
        if (type) {
          categories = mockCategories.filter(cat => cat.type === type);
        }
        dispatch({ type: 'SET_CATEGORIES', payload: categories });
      } else {
        // Use real API for authenticated users
        const categories = await categoryService.getCategories(type);
        dispatch({ type: 'SET_CATEGORIES', payload: categories });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [authState.isGuest]);

  const createCategory = useCallback(async (input: CategoryInput): Promise<Category> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda kategori ekleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const category = await categoryService.createCategory(input);
      dispatch({ type: 'ADD_CATEGORY', payload: category });
      return category;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const updateCategory = useCallback(async (
    id: string,
    updates: Partial<CategoryInput>
  ): Promise<Category> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda kategori düzenleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const category = await categoryService.updateCategory(id, updates);
      dispatch({ type: 'UPDATE_CATEGORY', payload: category });
      return category;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const deleteCategory = useCallback(async (id: string): Promise<void> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda kategori silemezsiniz. Lütfen giriş yapın.');
    }

    try {
      await categoryService.deleteCategory(id);
      dispatch({ type: 'DELETE_CATEGORY', payload: id });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const getCategoryById = useCallback((id: string): Category | undefined => {
    return state.categories.find(c => c.id === id);
  }, [state.categories]);

  const getIncomeCategories = useCallback((): Category[] => {
    return state.categories.filter(c => c.type === 'income');
  }, [state.categories]);

  const getExpenseCategories = useCallback((): Category[] => {
    return state.categories.filter(c => c.type === 'expense');
  }, [state.categories]);

  const initializeDefaultCategories = useCallback(async (): Promise<void> => {
    try {
      await categoryService.initializeDefaultCategories();
      await loadCategories();
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [loadCategories]);

  const value: CategoryContextType = {
    state,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryById,
    getIncomeCategories,
    getExpenseCategories,
    initializeDefaultCategories,
  };

  return (
    <CategoryContext.Provider value={value}>
      {children}
    </CategoryContext.Provider>
  );
};
