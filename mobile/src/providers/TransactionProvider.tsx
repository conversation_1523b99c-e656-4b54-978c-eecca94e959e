import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Transaction, TransactionInput } from '../models';
import { TransactionService } from '../services';
import { useAuth } from './AuthProvider';
import {
  mockTransactions,
  getMockTransactionsByDateRange,
  getMockTransactionsByCategory,
  getMockTransactionsByAccount,
  getMockRecentTransactions
} from '../data/mockData';

interface TransactionState {
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
  totalIncome: number;
  totalExpense: number;
  balance: number;
}

type TransactionAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TRANSACTIONS'; payload: Transaction[] }
  | { type: 'ADD_TRANSACTION'; payload: Transaction }
  | { type: 'UPDATE_TRANSACTION'; payload: Transaction }
  | { type: 'DELETE_TRANSACTION'; payload: string }
  | { type: 'CALCULATE_TOTALS' };

const initialState: TransactionState = {
  transactions: [],
  loading: false,
  error: null,
  totalIncome: 0,
  totalExpense: 0,
  balance: 0,
};

const transactionReducer = (state: TransactionState, action: TransactionAction): TransactionState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_TRANSACTIONS':
      const newState = { ...state, transactions: action.payload || [], loading: false, error: null };
      return calculateTotals(newState);
    case 'ADD_TRANSACTION':
      const addedState = { ...state, transactions: [action.payload, ...(state.transactions || [])] };
      return calculateTotals(addedState);
    case 'UPDATE_TRANSACTION':
      const updatedState = {
        ...state,
        transactions: (state.transactions || []).map(t =>
          t.id === action.payload.id ? action.payload : t
        ),
      };
      return calculateTotals(updatedState);
    case 'DELETE_TRANSACTION':
      const deletedState = {
        ...state,
        transactions: (state.transactions || []).filter(t => t.id !== action.payload),
      };
      return calculateTotals(deletedState);
    case 'CALCULATE_TOTALS':
      return calculateTotals(state);
    default:
      return state;
  }
};

const calculateTotals = (state: TransactionState): TransactionState => {
  // Ensure transactions is always an array
  const transactions = Array.isArray(state.transactions) ? state.transactions : [];

  const totalIncome = transactions
    .filter(t => t && t.type === 'income')
    .reduce((sum, t) => sum + (t.amount || 0), 0);

  const totalExpense = transactions
    .filter(t => t && t.type === 'expense')
    .reduce((sum, t) => sum + (t.amount || 0), 0);

  const balance = totalIncome - totalExpense;

  return {
    ...state,
    transactions,
    totalIncome,
    totalExpense,
    balance,
  };
};

interface TransactionContextType {
  state: TransactionState;
  loadTransactions: (startDate?: Date, endDate?: Date) => Promise<void>;
  createTransaction: (input: TransactionInput) => Promise<Transaction>;
  updateTransaction: (id: string, updates: Partial<TransactionInput>) => Promise<Transaction>;
  deleteTransaction: (id: string) => Promise<void>;
  getTransactionsByCategory: (categoryId: string) => Transaction[];
  getTransactionsByAccount: (accountId: string) => Transaction[];
  getCategoryTotals: (type: 'income' | 'expense') => Array<{ categoryId: string; total: number }>;
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export const useTransactions = () => {
  const context = useContext(TransactionContext);
  if (!context) {
    throw new Error('useTransactions must be used within a TransactionProvider');
  }
  return context;
};

interface TransactionProviderProps {
  children: React.ReactNode;
}

export const TransactionProvider: React.FC<TransactionProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(transactionReducer, initialState);
  const { state: authState } = useAuth();
  const transactionService = new TransactionService(authState.isGuest);

  // Update service guest mode and reload data when auth state changes
  useEffect(() => {
    transactionService.setGuestMode(authState.isGuest);
    loadTransactions();
  }, [authState.isAuthenticated, authState.isGuest]);

  const loadTransactions = useCallback(async (startDate?: Date, endDate?: Date) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      if (authState.isGuest) {
        // Use mock data for guest mode
        let transactions = mockTransactions;
        if (startDate && endDate) {
          transactions = getMockTransactionsByDateRange(startDate, endDate);
        }
        dispatch({ type: 'SET_TRANSACTIONS', payload: transactions });
      } else {
        // Use real API for authenticated users
        const transactions = await transactionService.getAllTransactions();
        dispatch({ type: 'SET_TRANSACTIONS', payload: transactions });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [authState.isGuest]);

  const createTransaction = useCallback(async (input: TransactionInput): Promise<Transaction> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda işlem ekleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const transaction = await transactionService.createTransaction(input);
      dispatch({ type: 'ADD_TRANSACTION', payload: transaction });
      return transaction;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const updateTransaction = useCallback(async (
    id: string,
    updates: Partial<TransactionInput>
  ): Promise<Transaction> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda işlem düzenleyemezsiniz. Lütfen giriş yapın.');
    }

    try {
      const transaction = await transactionService.updateTransaction(id, updates);
      dispatch({ type: 'UPDATE_TRANSACTION', payload: transaction });
      return transaction;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const deleteTransaction = useCallback(async (id: string): Promise<void> => {
    if (authState.isGuest) {
      throw new Error('Ziyaretçi modunda işlem silemezsiniz. Lütfen giriş yapın.');
    }

    try {
      await transactionService.deleteTransaction(id);
      dispatch({ type: 'DELETE_TRANSACTION', payload: id });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
      throw error;
    }
  }, [authState.isGuest]);

  const getTransactionsByCategory = useCallback((categoryId: string): Transaction[] => {
    const transactions = Array.isArray(state.transactions) ? state.transactions : [];
    return transactions.filter(t => t && t.categoryId === categoryId);
  }, [state.transactions]);

  const getTransactionsByAccount = useCallback((accountId: string): Transaction[] => {
    const transactions = Array.isArray(state.transactions) ? state.transactions : [];
    return transactions.filter(t => t && t.accountId === accountId);
  }, [state.transactions]);

  const getCategoryTotals = useCallback((type: 'income' | 'expense') => {
    const categoryTotals = new Map<string, number>();
    const transactions = Array.isArray(state.transactions) ? state.transactions : [];

    transactions
      .filter(t => t && t.type === type)
      .forEach(t => {
        const current = categoryTotals.get(t.categoryId) || 0;
        categoryTotals.set(t.categoryId, current + (t.amount || 0));
      });

    return Array.from(categoryTotals.entries()).map(([categoryId, total]) => ({
      categoryId,
      total,
    }));
  }, [state.transactions]);

  const value: TransactionContextType = {
    state,
    loadTransactions,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    getTransactionsByCategory,
    getTransactionsByAccount,
    getCategoryTotals,
  };

  return (
    <TransactionContext.Provider value={value}>
      {children}
    </TransactionContext.Provider>
  );
};
