import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Card } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { Transaction } from '../models';
import { CurrencyFormatter, DateFormatter } from '../utils';
import { useCategories, useAccounts } from '../providers';

interface TransactionCardProps {
  transaction: Transaction;
  onPress?: () => void;
  onLongPress?: () => void;
}

const TransactionCard: React.FC<TransactionCardProps> = ({
  transaction,
  onPress,
  onLongPress,
}) => {
  const { getCategoryById } = useCategories();
  const { getAccountById } = useAccounts();

  const category = getCategoryById(transaction.categoryId);
  const account = getAccountById(transaction.accountId);

  return (
    <TouchableOpacity onPress={onPress} onLongPress={onLongPress}>
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.header}>
            <View style={styles.leftSection}>
              <View style={[
                styles.iconContainer,
                { backgroundColor: category?.color || '#ccc' }
              ]}>
                <MaterialIcons 
                  name={category?.icon as any || 'category'} 
                  size={20} 
                  color="white" 
                />
              </View>
              <View style={styles.titleSection}>
                <Text style={styles.title}>{transaction.title}</Text>
                <Text style={styles.category}>{category?.name || 'Unknown'}</Text>
                <Text style={styles.account}>{account?.name || 'Unknown'}</Text>
              </View>
            </View>
            
            <View style={styles.rightSection}>
              <Text style={[
                styles.amount,
                transaction.type === 'income' ? styles.income : styles.expense
              ]}>
                {transaction.type === 'income' ? '+' : '-'}
                {CurrencyFormatter.format(transaction.amount, transaction.currency)}
              </Text>
              <Text style={styles.date}>
                {transaction.transactionDate
                  ? DateFormatter.getRelativeDate(transaction.transactionDate)
                  : 'Tarih yok'
                }
              </Text>
              <Text style={styles.paymentMethod}>
                {transaction.paymentMethod}
              </Text>
            </View>
          </View>
          
          {transaction.note && (
            <Text style={styles.note} numberOfLines={2}>
              {transaction.note}
            </Text>
          )}
          
          {transaction.location && (
            <View style={styles.locationContainer}>
              <MaterialIcons name="location-on" size={14} color="#666" />
              <Text style={styles.location}>{transaction.location}</Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 0,
    marginVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
  },
  leftSection: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  titleSection: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1D29',
    marginBottom: 4,
  },
  category: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    fontWeight: '500',
  },
  account: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '400',
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  income: {
    color: '#10B981',
  },
  expense: {
    color: '#EF4444',
  },
  date: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
    fontWeight: '500',
  },
  paymentMethod: {
    fontSize: 11,
    color: '#9CA3AF',
    fontWeight: '400',
  },
  note: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 12,
    fontStyle: 'italic',
    fontWeight: '400',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  location: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 6,
    fontWeight: '400',
  },
});

export default TransactionCard;
