import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from 'react-native-paper';
import { CurrencyFormatter } from '../utils';

interface SummaryCardProps {
  income: number;
  expense: number;
  balance: number;
  period: string;
  currency?: string;
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  income,
  expense,
  balance,
  period,
  currency = 'TRY',
}) => {
  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text style={styles.period}>{period}</Text>
        
        <View style={styles.row}>
          <View style={styles.item}>
            <Text style={styles.label}>Gelir</Text>
            <Text style={[styles.amount, styles.income]}>
              {CurrencyFormatter.format(income, currency)}
            </Text>
          </View>
          
          <View style={styles.item}>
            <Text style={styles.label}>Gider</Text>
            <Text style={[styles.amount, styles.expense]}>
              {CurrencyFormatter.format(expense, currency)}
            </Text>
          </View>
        </View>
        
        <View style={styles.balanceContainer}>
          <Text style={styles.label}>Net Bakiye</Text>
          <Text style={[
            styles.balance,
            balance >= 0 ? styles.positive : styles.negative
          ]}>
            {CurrencyFormatter.format(balance, currency)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 4,
  },
  period: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  item: {
    flex: 1,
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  amount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  income: {
    color: '#2ECC71',
  },
  expense: {
    color: '#E74C3C',
  },
  balanceContainer: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  balance: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  positive: {
    color: '#2ECC71',
  },
  negative: {
    color: '#E74C3C',
  },
});

export default SummaryCard;
