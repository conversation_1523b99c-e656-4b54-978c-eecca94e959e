import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { DatePickerModal } from './DatePickerModal';
import { DateFormatter } from '../../utils';

export interface DatePickerButtonProps {
  date: Date;
  onDateChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  label?: string;
  placeholder?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  disabled?: boolean;
  style?: any;
}

export const DatePickerButton: React.FC<DatePickerButtonProps> = ({
  date,
  onDateChange,
  mode = 'date',
  label,
  placeholder = 'Tarih seçin',
  minimumDate,
  maximumDate,
  disabled = false,
  style,
}) => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);

  const handleDateConfirm = (selectedDate: Date) => {
    setIsPickerOpen(false);
    onDateChange(selectedDate);
  };

  const handleDateCancel = () => {
    setIsPickerOpen(false);
  };

  const formatDate = () => {
    if (mode === 'time') {
      return DateFormatter.formatTime(date);
    } else if (mode === 'datetime') {
      return DateFormatter.formatDateTime(date);
    } else {
      return DateFormatter.formatDate(date);
    }
  };

  return (
    <View style={style}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity
        style={[styles.button, disabled && styles.disabled]}
        onPress={() => !disabled && setIsPickerOpen(true)}
        disabled={disabled}
      >
        <Text style={[styles.dateText, disabled && styles.disabledText]}>
          {date ? formatDate() : placeholder}
        </Text>
        <MaterialIcons 
          name="calendar-today" 
          size={20} 
          color={disabled ? '#ccc' : '#666'} 
        />
      </TouchableOpacity>

      <DatePickerModal
        open={isPickerOpen}
        date={date || new Date()}
        mode={mode}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        onConfirm={handleDateConfirm}
        onCancel={handleDateCancel}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1D29',
    marginBottom: 8,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  disabled: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  dateText: {
    fontSize: 16,
    color: '#1A1D29',
    fontWeight: '500',
    flex: 1,
  },
  disabledText: {
    color: '#9CA3AF',
  },
});
