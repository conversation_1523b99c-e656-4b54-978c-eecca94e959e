import React, { useState } from 'react';
import { Modal, View, StyleSheet, Text, TouchableOpacity, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';

export interface DatePickerModalProps {
  open: boolean;
  date: Date;
  mode?: 'date' | 'time' | 'datetime';
  locale?: string;
  title?: string;
  confirmText?: string;
  cancelText?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  onConfirm: (date: Date) => void;
  onCancel: () => void;
}

export const DatePickerModal: React.FC<DatePickerModalProps> = ({
  open,
  date,
  mode = 'date',
  locale = 'tr',
  title = '<PERSON><PERSON><PERSON>',
  confirmText = 'Tamam',
  cancelText = 'İptal',
  minimumDate,
  maximumDate,
  onConfirm,
  onCancel,
}) => {
  const [selectedDate, setSelectedDate] = useState(date);

  const handleConfirm = () => {
    onConfirm(selectedDate);
  };

  const handleDateChange = (event: any, newDate?: Date) => {
    if (Platform.OS === 'android') {
      if (event.type === 'set' && newDate) {
        onConfirm(newDate);
      } else {
        onCancel();
      }
    } else if (newDate) {
      setSelectedDate(newDate);
    }
  };

  if (!open) return null;

  if (Platform.OS === 'android') {
    return (
      <DateTimePicker
        value={selectedDate}
        mode={mode}
        display="default"
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        onChange={handleDateChange}
      />
    );
  }

  return (
    <Modal
      visible={open}
      transparent
      animationType="slide"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>{title}</Text>
          </View>

          <DateTimePicker
            value={selectedDate}
            mode={mode}
            display="spinner"
            minimumDate={minimumDate}
            maximumDate={maximumDate}
            onChange={handleDateChange}
            style={styles.picker}
          />

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelText}>{cancelText}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
              <Text style={styles.confirmText}>{confirmText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 16,
    margin: 20,
    minWidth: 300,
    maxWidth: '90%',
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    color: '#1A1D29',
  },
  picker: {
    height: 200,
  },
  buttonContainer: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  cancelButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
  },
  confirmButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  confirmText: {
    fontSize: 16,
    color: '#6366F1',
    fontWeight: '600',
  },
});
