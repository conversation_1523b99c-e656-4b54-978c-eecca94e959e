import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { theme } from '../../theme';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  onPress?: () => void;
  style?: ViewStyle;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'elevated',
  padding = 'md',
  onPress,
  style,
}) => {
  const cardStyle = [
    styles.base,
    styles[variant],
    padding !== 'none' && styles[`padding${padding.charAt(0).toUpperCase() + padding.slice(1)}` as keyof typeof styles],
    style,
  ];

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.95}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={cardStyle}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
  },

  // Variants
  elevated: {
    backgroundColor: theme.colors.surface.elevated,
    ...theme.shadows.card,
  },
  outlined: {
    backgroundColor: theme.colors.surface.primary,
    borderWidth: 1,
    borderColor: theme.colors.border.primary,
  },
  filled: {
    backgroundColor: theme.colors.surface.secondary,
  },

  // Padding
  paddingSm: {
    padding: theme.spacing.component.card.sm,
  },
  paddingMd: {
    padding: theme.spacing.component.card.md,
  },
  paddingLg: {
    padding: theme.spacing.component.card.lg,
  },
  paddingXl: {
    padding: theme.spacing.component.card.xl,
  },
});
