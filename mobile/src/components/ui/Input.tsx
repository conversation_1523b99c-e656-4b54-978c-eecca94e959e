import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { theme } from '../../theme';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof MaterialIcons.glyphMap;
  rightIcon?: keyof typeof MaterialIcons.glyphMap;
  onRightIconPress?: () => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  disabled?: boolean;
  required?: boolean;
  style?: ViewStyle;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  size = 'md',
  variant = 'outlined',
  disabled = false,
  required = false,
  style,
  secureTextEntry,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecure, setIsSecure] = useState(secureTextEntry);

  const containerStyle = [
    styles.container,
    style,
  ];

  const inputContainerStyle = [
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    error && styles.error,
    disabled && styles.disabled,
  ];

  const inputStyle = [
    styles.input,
    styles[`${size}Text`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
  ];

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;

  const handleToggleSecure = () => {
    setIsSecure(!isSecure);
  };

  return (
    <View style={containerStyle}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={inputContainerStyle}>
        {leftIcon && (
          <MaterialIcons
            name={leftIcon}
            size={iconSize}
            color={error ? theme.colors.error[500] : theme.colors.text.tertiary}
            style={styles.leftIcon}
          />
        )}
        
        <TextInput
          style={inputStyle}
          placeholderTextColor={theme.colors.text.tertiary}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          secureTextEntry={isSecure}
          {...props}
        />
        
        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            onPress={secureTextEntry ? handleToggleSecure : onRightIconPress}
            style={styles.rightIcon}
            disabled={!secureTextEntry && !onRightIconPress}
          >
            <MaterialIcons
              name={
                secureTextEntry
                  ? isSecure
                    ? 'visibility-off'
                    : 'visibility'
                  : rightIcon!
              }
              size={iconSize}
              color={error ? theme.colors.error[500] : theme.colors.text.tertiary}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {(error || hint) && (
        <Text style={[styles.helperText, error && styles.errorText]}>
          {error || hint}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing[4],
  },

  label: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing[2],
  },

  required: {
    color: theme.colors.error[500],
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
  },

  // Variants
  default: {
    backgroundColor: theme.colors.surface.primary,
    borderColor: theme.colors.border.primary,
  },
  filled: {
    backgroundColor: theme.colors.surface.secondary,
    borderColor: 'transparent',
  },
  outlined: {
    backgroundColor: theme.colors.surface.primary,
    borderColor: theme.colors.border.primary,
  },

  // Sizes
  sm: {
    height: theme.spacing.component.input.sm,
    paddingHorizontal: theme.spacing.component.padding.sm,
  },
  md: {
    height: theme.spacing.component.input.md,
    paddingHorizontal: theme.spacing.component.padding.md,
  },
  lg: {
    height: theme.spacing.component.input.lg,
    paddingHorizontal: theme.spacing.component.padding.lg,
  },

  // States
  focused: {
    borderColor: theme.colors.border.focus,
    borderWidth: 2,
  },
  error: {
    borderColor: theme.colors.border.error,
  },
  disabled: {
    backgroundColor: theme.colors.neutral[100],
    opacity: 0.6,
  },

  input: {
    flex: 1,
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.base,
  },

  inputWithLeftIcon: {
    marginLeft: theme.spacing[2],
  },

  inputWithRightIcon: {
    marginRight: theme.spacing[2],
  },

  // Text sizes
  smText: {
    fontSize: theme.typography.fontSize.sm,
  },
  mdText: {
    fontSize: theme.typography.fontSize.base,
  },
  lgText: {
    fontSize: theme.typography.fontSize.lg,
  },

  leftIcon: {
    marginLeft: theme.spacing[1],
  },

  rightIcon: {
    padding: theme.spacing[1],
    marginRight: theme.spacing[1],
  },

  helperText: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.text.tertiary,
    marginTop: theme.spacing[1],
    marginLeft: theme.spacing[1],
  },

  errorText: {
    color: theme.colors.error[500],
  },
});
