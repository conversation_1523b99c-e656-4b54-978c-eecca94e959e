import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { theme } from '../../theme';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'success' | 'error' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof MaterialIcons.glyphMap;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
}) => {
  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;
  const iconColor = variant === 'outline' || variant === 'ghost' 
    ? theme.colors.text.primary 
    : theme.colors.text.inverse;

  const renderIcon = () => {
    if (!icon) return null;
    
    return (
      <MaterialIcons
        name={icon}
        size={iconSize}
        color={disabled ? theme.colors.text.disabled : iconColor}
        style={iconPosition === 'right' ? styles.iconRight : styles.iconLeft}
      />
    );
  };

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' 
            ? theme.colors.primary[500] 
            : theme.colors.text.inverse
          }
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && renderIcon()}
          <Text style={textStyles}>{title}</Text>
          {icon && iconPosition === 'right' && renderIcon()}
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.button,
  },

  // Variants
  primary: {
    backgroundColor: theme.colors.primary[500],
  },
  secondary: {
    backgroundColor: theme.colors.secondary[500],
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.border.primary,
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  success: {
    backgroundColor: theme.colors.success[500],
  },
  error: {
    backgroundColor: theme.colors.error[500],
  },
  warning: {
    backgroundColor: theme.colors.warning[500],
  },

  // Sizes
  sm: {
    height: theme.spacing.component.button.sm,
    paddingHorizontal: theme.spacing.component.padding.sm,
  },
  md: {
    height: theme.spacing.component.button.md,
    paddingHorizontal: theme.spacing.component.padding.md,
  },
  lg: {
    height: theme.spacing.component.button.lg,
    paddingHorizontal: theme.spacing.component.padding.lg,
  },

  // States
  disabled: {
    opacity: 0.5,
  },
  fullWidth: {
    width: '100%',
  },

  // Text styles
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSize.base,
  },
  secondaryText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSize.base,
  },
  outlineText: {
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.base,
  },
  ghostText: {
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.base,
  },
  successText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSize.base,
  },
  errorText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSize.base,
  },
  warningText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSize.base,
  },

  // Size text styles
  smText: {
    fontSize: theme.typography.fontSize.sm,
  },
  mdText: {
    fontSize: theme.typography.fontSize.base,
  },
  lgText: {
    fontSize: theme.typography.fontSize.lg,
  },

  disabledText: {
    color: theme.colors.text.disabled,
  },

  // Icon styles
  iconLeft: {
    marginRight: theme.spacing[2],
  },
  iconRight: {
    marginLeft: theme.spacing[2],
  },
});
