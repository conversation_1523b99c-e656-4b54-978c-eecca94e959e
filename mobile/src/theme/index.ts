import { colors } from './colors';
import { typography } from './typography';
import { spacing } from './spacing';
import { shadows } from './shadows';

export const theme = {
  colors,
  typography,
  spacing,
  shadows,

  // Animation durations
  animation: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },

  // Border radius
  borderRadius: {
    none: 0,
    xs: 2,
    sm: 4,
    md: 6,
    lg: 8,
    xl: 12,
    '2xl': 16,
    '3xl': 24,
    full: 9999,
  },

  // Z-index values
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },

  // Breakpoints (for responsive design)
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  },

  // Component variants
  variants: {
    button: {
      primary: {
        backgroundColor: colors.primary[500],
        color: colors.text.inverse,
      },
      secondary: {
        backgroundColor: colors.secondary[500],
        color: colors.text.inverse,
      },
      outline: {
        backgroundColor: 'transparent',
        borderColor: colors.border.primary,
        color: colors.text.primary,
      },
      ghost: {
        backgroundColor: 'transparent',
        color: colors.text.primary,
      },
      success: {
        backgroundColor: colors.success[500],
        color: colors.text.inverse,
      },
      error: {
        backgroundColor: colors.error[500],
        color: colors.text.inverse,
      },
      warning: {
        backgroundColor: colors.warning[500],
        color: colors.text.inverse,
      },
    },

    card: {
      elevated: {
        backgroundColor: colors.surface.elevated,
        ...shadows.card,
      },
      outlined: {
        backgroundColor: colors.surface.primary,
        borderColor: colors.border.primary,
        borderWidth: 1,
      },
      filled: {
        backgroundColor: colors.surface.secondary,
      },
    },

    input: {
      default: {
        backgroundColor: colors.surface.primary,
        borderColor: colors.border.primary,
        color: colors.text.primary,
      },
      focused: {
        borderColor: colors.border.focus,
      },
      error: {
        borderColor: colors.border.error,
      },
      disabled: {
        backgroundColor: colors.neutral[100],
        color: colors.text.disabled,
      },
    },

    text: {
      primary: {
        color: colors.text.primary,
      },
      secondary: {
        color: colors.text.secondary,
      },
      tertiary: {
        color: colors.text.tertiary,
      },
      inverse: {
        color: colors.text.inverse,
      },
      success: {
        color: colors.success[600],
      },
      error: {
        color: colors.error[600],
      },
      warning: {
        color: colors.warning[600],
      },
      info: {
        color: colors.info[600],
      },
    },
  },

  // Component sizes
  sizes: {
    button: {
      sm: {
        height: spacing.component.button.sm,
        paddingHorizontal: spacing.component.padding.sm,
        ...typography.button.small,
      },
      md: {
        height: spacing.component.button.md,
        paddingHorizontal: spacing.component.padding.md,
        ...typography.button.medium,
      },
      lg: {
        height: spacing.component.button.lg,
        paddingHorizontal: spacing.component.padding.lg,
        ...typography.button.large,
      },
    },

    input: {
      sm: {
        height: spacing.component.input.sm,
        paddingHorizontal: spacing.component.padding.sm,
        ...typography.body.small,
      },
      md: {
        height: spacing.component.input.md,
        paddingHorizontal: spacing.component.padding.md,
        ...typography.body.medium,
      },
      lg: {
        height: spacing.component.input.lg,
        paddingHorizontal: spacing.component.padding.lg,
        ...typography.body.large,
      },
    },

    icon: spacing.component.icon,
    avatar: spacing.component.avatar,
  },
};

export type Theme = typeof theme;

export * from './colors';
export * from './typography';
export * from './spacing';
export * from './shadows';
