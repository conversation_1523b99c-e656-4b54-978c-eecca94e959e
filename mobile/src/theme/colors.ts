export const colors = {
  // Primary Colors
  primary: {
    50: '#E8F5E8',
    100: '#C3E6C3',
    200: '#9DD69D',
    300: '#76C576',
    400: '#58B858',
    500: '#2ECC71', // Main primary
    600: '#27A85F',
    700: '#1F8A4D',
    800: '#176C3B',
    900: '#0F4E29',
  },

  // Secondary Colors
  secondary: {
    50: '#F0F9FF',
    100: '#E0F2FE',
    200: '#BAE6FD',
    300: '#7DD3FC',
    400: '#38BDF8',
    500: '#0EA5E9',
    600: '#0284C7',
    700: '#0369A1',
    800: '#075985',
    900: '#0C4A6E',
  },

  // Neutral Colors
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },

  // Status Colors
  success: {
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
    800: '#166534',
    900: '#14532D',
  },

  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
  },

  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
  },

  info: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
  },

  // Semantic Colors
  income: '#22C55E',
  expense: '#EF4444',
  transfer: '#3B82F6',
  
  // Background Colors
  background: {
    primary: '#FFFFFF',
    secondary: '#F8FAFC',
    tertiary: '#F1F5F9',
  },

  // Surface Colors
  surface: {
    primary: '#FFFFFF',
    secondary: '#F8FAFC',
    elevated: '#FFFFFF',
  },

  // Text Colors
  text: {
    primary: '#0F172A',
    secondary: '#475569',
    tertiary: '#94A3B8',
    inverse: '#FFFFFF',
    disabled: '#CBD5E1',
  },

  // Border Colors
  border: {
    primary: '#E2E8F0',
    secondary: '#CBD5E1',
    focus: '#2ECC71',
    error: '#EF4444',
  },

  // Shadow Colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.05)',
    medium: 'rgba(0, 0, 0, 0.1)',
    heavy: 'rgba(0, 0, 0, 0.15)',
  },

  // Gradient Colors
  gradient: {
    primary: ['#2ECC71', '#27A85F'],
    secondary: ['#3B82F6', '#1D4ED8'],
    success: ['#22C55E', '#16A34A'],
    error: ['#EF4444', '#DC2626'],
    neutral: ['#F8FAFC', '#E2E8F0'],
  },
};

export type ColorPalette = typeof colors;

export type ColorScheme = 'light' | 'dark' | 'auto';

// Light theme colors
export const lightColors = colors;

// Dark theme colors
export const darkColors = {
  ...colors,

  // Background Colors
  background: {
    primary: '#0F172A',
    secondary: '#1E293B',
    tertiary: '#334155',
  },

  // Surface Colors
  surface: {
    primary: '#1E293B',
    secondary: '#334155',
    elevated: '#475569',
  },

  // Text Colors
  text: {
    primary: '#F8FAFC',
    secondary: '#CBD5E1',
    tertiary: '#94A3B8',
    inverse: '#0F172A',
    disabled: '#64748B',
  },

  // Border Colors
  border: {
    primary: '#475569',
    secondary: '#64748B',
    focus: '#2ECC71',
    error: '#EF4444',
  },

  // Shadow Colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.3)',
    medium: 'rgba(0, 0, 0, 0.5)',
    heavy: 'rgba(0, 0, 0, 0.7)',
  },
};
