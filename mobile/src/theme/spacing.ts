export const spacing = {
  // Base spacing unit (4px)
  unit: 4,

  // Spacing scale
  0: 0,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  11: 44,
  12: 48,
  14: 56,
  16: 64,
  20: 80,
  24: 96,
  28: 112,
  32: 128,
  36: 144,
  40: 160,
  44: 176,
  48: 192,
  52: 208,
  56: 224,
  60: 240,
  64: 256,
  72: 288,
  80: 320,
  96: 384,

  // Semantic spacing
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 80,
  '5xl': 96,

  // Component specific spacing
  component: {
    // Padding
    padding: {
      xs: 8,
      sm: 12,
      md: 16,
      lg: 20,
      xl: 24,
    },

    // Margin
    margin: {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
    },

    // Gap
    gap: {
      xs: 4,
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
    },

    // Border radius
    borderRadius: {
      none: 0,
      xs: 2,
      sm: 4,
      md: 6,
      lg: 8,
      xl: 12,
      '2xl': 16,
      '3xl': 24,
      full: 9999,
    },

    // Icon sizes
    icon: {
      xs: 12,
      sm: 16,
      md: 20,
      lg: 24,
      xl: 32,
      '2xl': 40,
      '3xl': 48,
    },

    // Button heights
    button: {
      sm: 32,
      md: 40,
      lg: 48,
      xl: 56,
    },

    // Input heights
    input: {
      sm: 32,
      md: 40,
      lg: 48,
    },

    // Card padding
    card: {
      sm: 12,
      md: 16,
      lg: 20,
      xl: 24,
    },

    // Screen padding
    screen: {
      horizontal: 16,
      vertical: 20,
    },

    // Header heights
    header: {
      sm: 48,
      md: 56,
      lg: 64,
    },

    // Tab bar height
    tabBar: {
      height: 60,
    },

    // FAB size
    fab: {
      sm: 40,
      md: 56,
      lg: 64,
    },

    // Avatar sizes
    avatar: {
      xs: 24,
      sm: 32,
      md: 40,
      lg: 48,
      xl: 64,
      '2xl': 80,
    },

    // Divider thickness
    divider: {
      thin: 1,
      medium: 2,
      thick: 4,
    },
  },
};

export type Spacing = typeof spacing;
