import { Platform } from 'react-native';

const fontFamily = {
  regular: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),
  medium: Platform.select({
    ios: 'System',
    android: 'Roboto-Medium',
    default: 'System',
  }),
  bold: Platform.select({
    ios: 'System',
    android: 'Roboto-Bold',
    default: 'System',
  }),
  light: Platform.select({
    ios: 'System',
    android: 'Roboto-Light',
    default: 'System',
  }),
};

export const typography = {
  // Font Families
  fontFamily,

  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
    '7xl': 72,
    '8xl': 96,
    '9xl': 128,
  },

  // Line Heights
  lineHeight: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },

  // Font Weights
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  // Letter Spacing
  letterSpacing: {
    tighter: -0.05,
    tight: -0.025,
    normal: 0,
    wide: 0.025,
    wider: 0.05,
    widest: 0.1,
  },

  // Text Styles
  heading: {
    h1: {
      fontSize: 32,
      fontWeight: '700',
      lineHeight: 1.25,
      letterSpacing: -0.025,
    },
    h2: {
      fontSize: 28,
      fontWeight: '600',
      lineHeight: 1.3,
      letterSpacing: -0.025,
    },
    h3: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 1.35,
      letterSpacing: 0,
    },
    h4: {
      fontSize: 20,
      fontWeight: '600',
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    h5: {
      fontSize: 18,
      fontWeight: '500',
      lineHeight: 1.45,
      letterSpacing: 0,
    },
    h6: {
      fontSize: 16,
      fontWeight: '500',
      lineHeight: 1.5,
      letterSpacing: 0,
    },
  },

  body: {
    large: {
      fontSize: 18,
      fontWeight: '400',
      lineHeight: 1.6,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    small: {
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 1.45,
      letterSpacing: 0,
    },
    xs: {
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 1.4,
      letterSpacing: 0,
    },
  },

  caption: {
    large: {
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 1.4,
      letterSpacing: 0.025,
    },
    medium: {
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 1.35,
      letterSpacing: 0.025,
    },
    small: {
      fontSize: 10,
      fontWeight: '500',
      lineHeight: 1.3,
      letterSpacing: 0.05,
    },
  },

  button: {
    large: {
      fontSize: 16,
      fontWeight: '600',
      lineHeight: 1.25,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 14,
      fontWeight: '600',
      lineHeight: 1.25,
      letterSpacing: 0,
    },
    small: {
      fontSize: 12,
      fontWeight: '600',
      lineHeight: 1.25,
      letterSpacing: 0.025,
    },
  },

  label: {
    large: {
      fontSize: 16,
      fontWeight: '500',
      lineHeight: 1.25,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 1.25,
      letterSpacing: 0,
    },
    small: {
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 1.25,
      letterSpacing: 0.025,
    },
  },
};

export type Typography = typeof typography;
