import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../providers/AuthProvider';
import { RootStackParamList } from '../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<RootStackParamList>;

export const useGuestGuard = () => {
  const { state: authState } = useAuth();
  const navigation = useNavigation<NavigationProp>();

  const requireAuth = useCallback((
    action: () => void,
    options?: {
      title?: string;
      message?: string;
      showRegister?: boolean;
    }
  ) => {
    if (authState.isGuest) {
      const title = options?.title || 'Giriş Gerekli';
      const message = options?.message || 'Bu işlemi gerçekleştirmek için hesabınıza giriş yapmanız gerekiyor.';
      const showRegister = options?.showRegister !== false;

      const buttons = [
        { text: 'İptal', style: 'cancel' as const },
        {
          text: '<PERSON><PERSON><PERSON>p',
          onPress: () => {
            navigation.navigate('Login');
          },
        },
      ];

      if (showRegister) {
        buttons.push({
          text: 'Hesap Oluştur',
          onPress: () => {
            navigation.navigate('Register');
          },
        });
      }

      Alert.alert(title, message, buttons);
      return false;
    }

    if (!authState.isAuthenticated) {
      Alert.alert(
        'Giriş Gerekli',
        'Bu işlemi gerçekleştirmek için hesabınıza giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Giriş Yap',
            onPress: () => {
              navigation.navigate('Login');
            },
          },
        ]
      );
      return false;
    }

    // User is authenticated, execute the action
    action();
    return true;
  }, [authState.isGuest, authState.isAuthenticated, navigation]);

  const isGuest = authState.isGuest;
  const isAuthenticated = authState.isAuthenticated;

  return {
    requireAuth,
    isGuest,
    isAuthenticated,
  };
};
