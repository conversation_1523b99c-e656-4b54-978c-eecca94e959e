import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../providers/AuthProvider';
import { RootStackParamList } from '../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<RootStackParamList>;

export interface AuthGuardOptions {
  title?: string;
  message?: string;
  showRegisterOption?: boolean;
  onAuthRequired?: () => void;
}

export const useAuthGuard = () => {
  const { state } = useAuth();
  const navigation = useNavigation<NavigationProp>();

  const requireAuth = useCallback(
    (
      action: () => void | Promise<void>,
      options: AuthGuardOptions = {}
    ): void => {
      const {
        title = 'Giri<PERSON> Gerekli',
        message = 'Bu işlemi gerçekleştirmek için giriş yapmanız gerekiyor.',
        showRegisterOption = true,
        onAuthRequired,
      } = options;

      if (state.isAuthenticated) {
        // User is authenticated, execute the action
        if (typeof action === 'function') {
          const result = action();
          if (result instanceof Promise) {
            result.catch((error) => {
              console.error('Action execution error:', error);
            });
          }
        }
      } else {
        // User is not authenticated, show auth prompt
        if (onAuthRequired) {
          onAuthRequired();
          return;
        }

        const buttons = [
          {
            text: 'İptal',
            style: 'cancel' as const,
          },
          {
            text: 'Giriş Yap',
            onPress: () => navigation.navigate('Login'),
          },
        ];

        if (showRegisterOption) {
          buttons.push({
            text: 'Hesap Oluştur',
            onPress: () => navigation.navigate('Register'),
          });
        }

        Alert.alert(title, message, buttons);
      }
    },
    [state.isAuthenticated, navigation]
  );

  const isAuthenticated = useCallback((): boolean => {
    return state.isAuthenticated;
  }, [state.isAuthenticated]);

  const isGuest = useCallback((): boolean => {
    return state.isGuest;
  }, [state.isGuest]);

  const canPerformAction = useCallback(
    (requiresAuth: boolean = true): boolean => {
      if (!requiresAuth) return true;
      return state.isAuthenticated;
    },
    [state.isAuthenticated]
  );

  const promptForAuth = useCallback(
    (options: AuthGuardOptions = {}): void => {
      const {
        title = 'Giriş Gerekli',
        message = 'Bu özelliği kullanmak için giriş yapmanız gerekiyor.',
        showRegisterOption = true,
      } = options;

      const buttons = [
        {
          text: 'İptal',
          style: 'cancel' as const,
        },
        {
          text: 'Giriş Yap',
          onPress: () => navigation.navigate('Login'),
        },
      ];

      if (showRegisterOption) {
        buttons.push({
          text: 'Hesap Oluştur',
          onPress: () => navigation.navigate('Register'),
        });
      }

      Alert.alert(title, message, buttons);
    },
    [navigation]
  );

  const withAuthCheck = useCallback(
    <T extends any[], R>(
      fn: (...args: T) => R,
      options: AuthGuardOptions = {}
    ) => {
      return (...args: T): R | void => {
        if (state.isAuthenticated) {
          return fn(...args);
        } else {
          promptForAuth(options);
        }
      };
    },
    [state.isAuthenticated, promptForAuth]
  );

  return {
    requireAuth,
    isAuthenticated,
    isGuest,
    canPerformAction,
    promptForAuth,
    withAuthCheck,
  };
};
