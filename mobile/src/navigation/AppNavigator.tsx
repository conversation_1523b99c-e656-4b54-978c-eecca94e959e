import React from 'react';
import { TouchableOpacity, View, Image, Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { MaterialIcons } from '@expo/vector-icons';

// Screens
import HomeScreen from '../screens/HomeScreen';
import TransactionListScreen from '../screens/TransactionListScreen';
import AddEditTransactionScreen from '../screens/AddEditTransactionScreen';
import CategoriesScreen from '../screens/CategoriesScreen';
import AccountsScreen from '../screens/AccountsScreen';
import ReportsScreen from '../screens/ReportsScreen';
import SettingsScreen from '../screens/SettingsScreen';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import BudgetScreen from '../screens/BudgetScreen';
import BankStatementScreen from '../screens/BankStatementScreen';
import WelcomeScreen from '../screens/WelcomeScreen';
import ProfileScreen from '../screens/ProfileScreen';

export type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  Main: { screen?: string } | undefined;
  AddEditTransaction: { transactionId?: string };
  TransactionList: undefined;
  Budget: undefined;
  BankStatement: undefined;
  Profile: undefined;
};

export type TabParamList = {
  Home: undefined;
  Reports: undefined;
  Budget: undefined;
  Categories: undefined;
  Accounts: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          // Increase icon size
          const iconSize = 28;
          let iconName: keyof typeof MaterialIcons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Reports':
              iconName = 'bar-chart';
              break;
            case 'Budget':
              iconName = 'account-balance-wallet';
              break;
            case 'Categories':
              iconName = 'category';
              break;
            case 'Accounts':
              iconName = 'account-balance';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'home';
          }

          return <MaterialIcons name={iconName} size={iconSize} color={color} />;
        },
        tabBarActiveTintColor: '#2ECC71',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          height: 80,
          paddingBottom: 20,
          paddingTop: 15,
          marginBottom: 10,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 5,
        },
        tabBarAllowFontScaling: false,
        headerShown: true,
        headerStyle: {
          backgroundColor: '#6366F1',
          paddingTop: 10,
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 20,
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={({ navigation }) => ({
          headerTitle: () => (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Image
                source={require('../../assets/Butce360Logo.png')}
                style={{ width: 32, height: 32, borderRadius: 8, marginRight: 12 }}
                resizeMode="contain"
              />
              <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#FFFFFF' }}>
                Butce360
              </Text>
            </View>
          ),
          tabBarLabel: 'Ana Sayfa',
          headerRight: () => (
            <TouchableOpacity
              onPress={() => (navigation as any).navigate('Profile')}
              style={{ marginRight: 15 }}
            >
              <MaterialIcons name="account-circle" size={28} color="#FFFFFF" />
            </TouchableOpacity>
          ),
        })}
      />
      <Tab.Screen
        name="Reports"
        component={ReportsScreen}
        options={{ title: 'Rapor' }}
      />
      <Tab.Screen
        name="Budget"
        component={BudgetScreen}
        options={{ title: 'Bütçe' }}
      />
      <Tab.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{ title: 'Kategori' }}
      />
      <Tab.Screen
        name="Accounts"
        component={AccountsScreen}
        options={{ title: 'Hesap' }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{ title: 'Ayar' }}
      />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Welcome"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#6366F1',
            paddingTop: 10,
          },
          headerTintColor: '#FFFFFF',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: 20,
          },
          headerBackTitle: 'Geri',
        }}
      >
        <Stack.Screen
          name="Welcome"
          component={WelcomeScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Register"
          component={RegisterScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Main"
          component={TabNavigator}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="AddEditTransaction"
          component={AddEditTransactionScreen}
          options={{
            title: 'İşlem Ekle/Düzenle',
            presentation: 'modal'
          }}
        />
        <Stack.Screen
          name="TransactionList"
          component={TransactionListScreen}
          options={{ title: 'Tüm İşlemler' }}
        />
        <Stack.Screen
          name="Budget"
          component={BudgetScreen}
          options={{ title: 'Bütçe Yönetimi' }}
        />
        <Stack.Screen
          name="BankStatement"
          component={BankStatementScreen}
          options={{ title: 'Ekstre İşleme' }}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={{ title: 'Profil' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
