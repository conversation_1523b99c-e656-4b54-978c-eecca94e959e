import React, { useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../providers/AuthProvider';
import { RootStackParamList } from './AppNavigator';

type AuthNavigatorProps = {
  children: React.ReactNode;
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

const AuthNavigator: React.FC<AuthNavigatorProps> = ({ children }) => {
  const { state } = useAuth();
  const navigation = useNavigation<NavigationProp>();

  useEffect(() => {
    if (!state.loading) {
      if (state.isAuthenticated) {
        // User is authenticated, navigate to main app
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }],
        });
      } else if (state.isGuest) {
        // User is in guest mode, navigate to main app
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }],
        });
      } else {
        // User is not authenticated and not in guest mode, show welcome
        navigation.reset({
          index: 0,
          routes: [{ name: 'Welcome' }],
        });
      }
    }
  }, [state.loading, state.isAuthenticated, state.isGuest, navigation]);

  if (state.loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

export default AuthNavigator;
