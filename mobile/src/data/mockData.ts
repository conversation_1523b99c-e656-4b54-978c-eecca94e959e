import { Transaction } from '../models/Transaction';
import { Account } from '../models/Account';
import { Category } from '../models/Category';
import { Budget } from '../services/BudgetService';

// Mock Categories
export const mockCategories: Category[] = [
  {
    id: 'mock-cat-1',
    name: 'Market',
    type: 'expense',
    icon: 'shopping-cart',
    color: '#FF6B6B',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  },
  {
    id: 'mock-cat-2',
    name: 'Restoran',
    type: 'expense',
    icon: 'restaurant',
    color: '#4ECDC4',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  },
  {
    id: 'mock-cat-3',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    type: 'expense',
    icon: 'directions-car',
    color: '#45B7D1',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  },
  {
    id: 'mock-cat-4',
    name: '<PERSON><PERSON><PERSON>ce',
    type: 'expense',
    icon: 'movie',
    color: '#96CEB4',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  },
  {
    id: 'mock-cat-5',
    name: 'Maaş',
    type: 'income',
    icon: 'work',
    color: '#FFEAA7',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  },
  {
    id: 'mock-cat-6',
    name: 'Freelance',
    type: 'income',
    icon: 'computer',
    color: '#DDA0DD',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  },
];

// Mock Accounts
export const mockAccounts: Account[] = [
  {
    id: 'mock-acc-1',
    name: 'Ana Hesap',
    type: 'checking',
    balance: 15750.50,
    currency: 'TRY',
    color: '#6366F1',
    icon: 'account-balance',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-08-18'),
  },
  {
    id: 'mock-acc-2',
    name: 'Birikim Hesabı',
    type: 'savings',
    balance: 45000.00,
    currency: 'TRY',
    color: '#10B981',
    icon: 'savings',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-08-18'),
  },
  {
    id: 'mock-acc-3',
    name: 'Kredi Kartı',
    type: 'credit',
    balance: -2340.75,
    currency: 'TRY',
    color: '#EF4444',
    icon: 'credit-card',
    isDefault: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-08-18'),
  },
];

// Mock Transactions
export const mockTransactions: Transaction[] = [
  {
    id: 'mock-trx-1',
    title: 'Migros Market Alışverişi',
    type: 'expense',
    amount: 285.50,
    currency: 'TRY',
    categoryId: 'mock-cat-1',
    paymentMethod: 'card',
    accountId: 'mock-acc-1',
    note: 'Haftalık market alışverişi',
    transactionDate: new Date('2025-08-18T10:30:00'),
    location: 'Migros Ataşehir',
    createdAt: new Date('2025-08-18T10:30:00'),
    updatedAt: new Date('2025-08-18T10:30:00'),
  },
  {
    id: 'mock-trx-2',
    title: 'Starbucks Kahve',
    type: 'expense',
    amount: 45.00,
    currency: 'TRY',
    categoryId: 'mock-cat-2',
    paymentMethod: 'card',
    accountId: 'mock-acc-1',
    note: 'Öğle arası kahve',
    transactionDate: new Date('2025-08-18T14:15:00'),
    location: 'Starbucks Zorlu Center',
    createdAt: new Date('2025-08-18T14:15:00'),
    updatedAt: new Date('2025-08-18T14:15:00'),
  },
  {
    id: 'mock-trx-3',
    title: 'Metro Kartı Yükleme',
    type: 'expense',
    amount: 100.00,
    currency: 'TRY',
    categoryId: 'mock-cat-3',
    paymentMethod: 'cash',
    accountId: 'mock-acc-1',
    note: 'Haftalık ulaşım',
    transactionDate: new Date('2025-08-17T08:00:00'),
    location: 'Ataşehir Metro',
    createdAt: new Date('2025-08-17T08:00:00'),
    updatedAt: new Date('2025-08-17T08:00:00'),
  },
  {
    id: 'mock-trx-4',
    title: 'Sinema Bileti',
    type: 'expense',
    amount: 75.00,
    currency: 'TRY',
    categoryId: 'mock-cat-4',
    paymentMethod: 'card',
    accountId: 'mock-acc-1',
    note: 'Aksiyon filmi',
    transactionDate: new Date('2025-08-16T20:00:00'),
    location: 'Cinemaximum Zorlu',
    createdAt: new Date('2025-08-16T20:00:00'),
    updatedAt: new Date('2025-08-16T20:00:00'),
  },
  {
    id: 'mock-trx-5',
    title: 'Maaş Ödemesi',
    type: 'income',
    amount: 12500.00,
    currency: 'TRY',
    categoryId: 'mock-cat-5',
    paymentMethod: 'transfer',
    accountId: 'mock-acc-1',
    note: 'Ağustos maaşı',
    transactionDate: new Date('2025-08-15T09:00:00'),
    location: 'Şirket',
    createdAt: new Date('2025-08-15T09:00:00'),
    updatedAt: new Date('2025-08-15T09:00:00'),
  },
  {
    id: 'mock-trx-6',
    title: 'Freelance Proje',
    type: 'income',
    amount: 3500.00,
    currency: 'TRY',
    categoryId: 'mock-cat-6',
    paymentMethod: 'transfer',
    accountId: 'mock-acc-2',
    note: 'Web sitesi geliştirme',
    transactionDate: new Date('2025-08-14T16:30:00'),
    location: 'Online',
    createdAt: new Date('2025-08-14T16:30:00'),
    updatedAt: new Date('2025-08-14T16:30:00'),
  },
  {
    id: 'mock-trx-7',
    title: 'Benzin',
    type: 'expense',
    amount: 350.00,
    currency: 'TRY',
    categoryId: 'mock-cat-3',
    paymentMethod: 'card',
    accountId: 'mock-acc-1',
    note: 'Depo doldurma',
    transactionDate: new Date('2025-08-13T18:45:00'),
    location: 'Shell Ataşehir',
    createdAt: new Date('2025-08-13T18:45:00'),
    updatedAt: new Date('2025-08-13T18:45:00'),
  },
  {
    id: 'mock-trx-8',
    title: 'Akşam Yemeği',
    type: 'expense',
    amount: 180.00,
    currency: 'TRY',
    categoryId: 'mock-cat-2',
    paymentMethod: 'card',
    accountId: 'mock-acc-1',
    note: 'Arkadaşlarla yemek',
    transactionDate: new Date('2025-08-12T19:30:00'),
    location: 'Nusr-Et Steakhouse',
    createdAt: new Date('2025-08-12T19:30:00'),
    updatedAt: new Date('2025-08-12T19:30:00'),
  },
];

// Mock Budgets
export const mockBudgets: Budget[] = [
  {
    id: 'mock-budget-1',
    amount: 1500.00,
    spent: 285.50,
    remaining: 1214.50,
    categoryId: 'mock-cat-1',
    categoryName: 'Market',
    period: 'monthly',
    percentage: 19,
    createdAt: new Date('2024-08-01'),
    updatedAt: new Date('2025-08-18'),
  },
  {
    id: 'mock-budget-2',
    amount: 800.00,
    spent: 225.00,
    remaining: 575.00,
    categoryId: 'mock-cat-2',
    categoryName: 'Restoran',
    period: 'monthly',
    percentage: 28,
    createdAt: new Date('2024-08-01'),
    updatedAt: new Date('2025-08-18'),
  },
  {
    id: 'mock-budget-3',
    amount: 600.00,
    spent: 450.00,
    remaining: 150.00,
    categoryId: 'mock-cat-3',
    categoryName: 'Ulaşım',
    period: 'monthly',
    percentage: 75,
    createdAt: new Date('2024-08-01'),
    updatedAt: new Date('2025-08-18'),
  },
  {
    id: 'mock-budget-4',
    amount: 400.00,
    spent: 75.00,
    remaining: 325.00,
    categoryId: 'mock-cat-4',
    categoryName: 'Eğlence',
    period: 'monthly',
    percentage: 19,
    createdAt: new Date('2024-08-01'),
    updatedAt: new Date('2025-08-18'),
  },
];

// Mock User
export const mockUser = {
  id: 'mock-user-1',
  name: 'Demo Kullanıcı',
  email: '<EMAIL>',
  username: 'demo',
};

// Helper functions
export const getMockTransactionsByDateRange = (startDate: Date, endDate: Date): Transaction[] => {
  return mockTransactions.filter(transaction => {
    const transactionDate = new Date(transaction.transactionDate);
    return transactionDate >= startDate && transactionDate <= endDate;
  });
};

export const getMockTransactionsByCategory = (categoryId: string): Transaction[] => {
  return mockTransactions.filter(transaction => transaction.categoryId === categoryId);
};

export const getMockTransactionsByAccount = (accountId: string): Transaction[] => {
  return mockTransactions.filter(transaction => transaction.accountId === accountId);
};

export const getMockRecentTransactions = (limit: number = 10): Transaction[] => {
  return mockTransactions
    .sort((a, b) => new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime())
    .slice(0, limit);
};
