import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { FAB, Appbar } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { SummaryCard, TransactionCard } from '../components';
import { useTransactions, useCategories, useAccounts, useAuth } from '../providers';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { useGuestGuard } from '../hooks/useGuestGuard';
import { DateFormatter } from '../utils';
import { RootStackParamList } from '../navigation/AppNavigator';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { state: transactionState, loadTransactions } = useTransactions();
  const { loadCategories, initializeDefaultCategories } = useCategories();
  const { loadAccounts, initializeDefaultAccounts } = useAccounts();
  const { state: authState } = useAuth();
  const { requireAuth } = useAuthGuard();
  const { requireAuth: requireAuthGuest } = useGuestGuard();

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [refreshing, setRefreshing] = useState(false);

  const loadData = useCallback(async () => {
    try {
      const startDate = DateFormatter.getStartOfMonth(selectedDate);
      const endDate = DateFormatter.getEndOfMonth(selectedDate);

      if (authState.isGuest) {
        // Guest mode: Load mock data
        await Promise.all([
          initializeDefaultCategories(),
          initializeDefaultAccounts(),
          loadCategories(),
          loadAccounts(),
          loadTransactions(startDate, endDate),
        ]);
      } else {
        // Authenticated mode: Load real data
        await Promise.all([
          initializeDefaultCategories(),
          initializeDefaultAccounts(),
          loadCategories(),
          loadAccounts(),
          loadTransactions(startDate, endDate),
        ]);
      }
    } catch (error) {
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      console.error('Error loading data:', error);
    }
  }, [selectedDate, authState.isGuest, loadTransactions, loadCategories, loadAccounts, initializeDefaultCategories, initializeDefaultAccounts]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  const selectPreviousMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setSelectedDate(newDate);
  };

  const selectNextMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setSelectedDate(newDate);
  };

  const handleAddTransaction = () => {
    requireAuthGuest(() => {
      navigation.navigate('AddEditTransaction', {});
    }, {
      title: 'İşlem Eklemek İçin Giriş Yapın',
      message: 'Yeni işlem eklemek için hesabınıza giriş yapmanız gerekiyor.',
    });
  };

  const handleTransactionPress = (transactionId: string) => {
    navigation.navigate('AddEditTransaction', { transactionId });
  };

  const handleViewAllTransactions = () => {
    navigation.navigate('TransactionList');
  };

  const recentTransactions = transactionState.transactions.slice(0, 5);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Month Selector */}
        <View style={styles.monthSelector}>
          <TouchableOpacity onPress={selectPreviousMonth}>
            <MaterialIcons name="chevron-left" size={30} color="#333" />
          </TouchableOpacity>
          
          <Text style={styles.monthText}>
            {DateFormatter.formatMonth(selectedDate)}
          </Text>
          
          <TouchableOpacity onPress={selectNextMonth}>
            <MaterialIcons name="chevron-right" size={30} color="#333" />
          </TouchableOpacity>
        </View>

        {/* Summary Card */}
        <SummaryCard
          income={transactionState.totalIncome}
          expense={transactionState.totalExpense}
          balance={transactionState.balance}
          period={DateFormatter.formatMonth(selectedDate)}
        />

        {/* Recent Transactions */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Son İşlemler</Text>
          <TouchableOpacity onPress={handleViewAllTransactions}>
            <Text style={styles.seeAllText}>Tümünü Gör</Text>
          </TouchableOpacity>
        </View>

        {recentTransactions.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialIcons name="receipt" size={64} color="#ccc" />
            <Text style={styles.emptyText}>
              Henüz işlem yok. İlk işleminizi ekleyin!
            </Text>
          </View>
        ) : (
          recentTransactions.map((transaction) => (
            <TransactionCard
              key={transaction.id}
              transaction={transaction}
              onPress={() => handleTransactionPress(transaction.id!)}
            />
          ))
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="plus"
        label="İşlem Ekle"
        onPress={handleAddTransaction}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  scrollView: {
    flex: 1,
  },
  monthSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  monthText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1D29',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1D29',
  },
  seeAllText: {
    fontSize: 14,
    color: '#6366F1',
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 24,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
  bottomSpacing: {
    height: 120,
  },
  fab: {
    position: 'absolute',
    margin: 20,
    right: 0,
    bottom: 0,
    backgroundColor: '#6366F1',
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default HomeScreen;
