import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Appbar, Card, SegmentedButtons } from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, BarChart } from 'react-native-chart-kit';

import { useTransactions, useCategories } from '../providers';
import { DateFormatter, CurrencyFormatter } from '../utils';

const screenWidth = Dimensions.get('window').width;

const ReportsScreen: React.FC = () => {
  const { state: transactionState, loadTransactions, getCategoryTotals } = useTransactions();
  const { state: categoryState } = useCategories();
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'year'>('month');
  const [selectedDate, setSelectedDate] = useState(new Date());

  useEffect(() => {
    const startDate = selectedPeriod === 'month' 
      ? DateFormatter.getStartOfMonth(selectedDate)
      : new Date(selectedDate.getFullYear(), 0, 1);
    
    const endDate = selectedPeriod === 'month'
      ? DateFormatter.getEndOfMonth(selectedDate)
      : new Date(selectedDate.getFullYear(), 11, 31);

    loadTransactions(startDate, endDate);
  }, [selectedPeriod, selectedDate]);

  const expenseCategoryTotals = getCategoryTotals('expense');
  const incomeCategoryTotals = getCategoryTotals('income');

  const getChartData = (categoryTotals: Array<{ categoryId: string; total: number }>) => {
    return categoryTotals.map((item, index) => {
      const category = categoryState.categories.find(c => c.id === item.categoryId);
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
      
      return {
        name: category?.name || 'Unknown',
        population: item.total,
        color: category?.color || colors[index % colors.length],
        legendFontColor: '#7F7F7F',
        legendFontSize: 12,
      };
    });
  };

  const expenseChartData = getChartData(expenseCategoryTotals);
  const incomeChartData = getChartData(incomeCategoryTotals);

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    color: (opacity = 1) => `rgba(46, 204, 113, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#ffa726',
    },
  };

  const periodLabel = selectedPeriod === 'month' 
    ? DateFormatter.formatMonth(selectedDate)
    : selectedDate.getFullYear().toString();

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <SegmentedButtons
            value={selectedPeriod}
            onValueChange={(value) => setSelectedPeriod(value as 'month' | 'year')}
            buttons={[
              { value: 'month', label: 'Aylık' },
              { value: 'year', label: 'Yıllık' },
            ]}
          />
        </View>

        {/* Summary Card */}
        <Card style={styles.summaryCard}>
          <Card.Content>
            <Text style={styles.periodTitle}>{periodLabel}</Text>
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Toplam Gelir</Text>
                <Text style={[styles.summaryValue, styles.income]}>
                  {CurrencyFormatter.format(transactionState.totalIncome)}
                </Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>Toplam Gider</Text>
                <Text style={[styles.summaryValue, styles.expense]}>
                  {CurrencyFormatter.format(transactionState.totalExpense)}
                </Text>
              </View>
            </View>
            <View style={styles.balanceContainer}>
              <Text style={styles.summaryLabel}>Net Bakiye</Text>
              <Text style={[
                styles.balanceValue,
                transactionState.balance >= 0 ? styles.income : styles.expense
              ]}>
                {CurrencyFormatter.format(transactionState.balance)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Expense Chart */}
        {expenseChartData.length > 0 && (
          <Card style={styles.chartCard}>
            <Card.Content>
              <Text style={styles.chartTitle}>Gider Dağılımı</Text>
              <PieChart
                data={expenseChartData}
                width={screenWidth - 64}
                height={220}
                chartConfig={chartConfig}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                absolute
              />
            </Card.Content>
          </Card>
        )}

        {/* Income Chart */}
        {incomeChartData.length > 0 && (
          <Card style={styles.chartCard}>
            <Card.Content>
              <Text style={styles.chartTitle}>Gelir Dağılımı</Text>
              <PieChart
                data={incomeChartData}
                width={screenWidth - 64}
                height={220}
                chartConfig={chartConfig}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                absolute
              />
            </Card.Content>
          </Card>
        )}

        {/* Category Breakdown */}
        <Card style={styles.breakdownCard}>
          <Card.Content>
            <Text style={styles.chartTitle}>Kategori Detayları</Text>
            
            {expenseCategoryTotals.length > 0 && (
              <>
                <Text style={styles.breakdownSubtitle}>Giderler</Text>
                {expenseCategoryTotals.map((item) => {
                  const category = categoryState.categories.find(c => c.id === item.categoryId);
                  const percentage = (item.total / transactionState.totalExpense) * 100;
                  
                  return (
                    <View key={item.categoryId} style={styles.breakdownItem}>
                      <View style={styles.breakdownLeft}>
                        <View style={[
                          styles.breakdownColor,
                          { backgroundColor: category?.color || '#ccc' }
                        ]} />
                        <Text style={styles.breakdownName}>
                          {category?.name || 'Unknown'}
                        </Text>
                      </View>
                      <View style={styles.breakdownRight}>
                        <Text style={styles.breakdownAmount}>
                          {CurrencyFormatter.format(item.total)}
                        </Text>
                        <Text style={styles.breakdownPercentage}>
                          {percentage.toFixed(1)}%
                        </Text>
                      </View>
                    </View>
                  );
                })}
              </>
            )}

            {incomeCategoryTotals.length > 0 && (
              <>
                <Text style={[styles.breakdownSubtitle, { marginTop: 16 }]}>Gelirler</Text>
                {incomeCategoryTotals.map((item) => {
                  const category = categoryState.categories.find(c => c.id === item.categoryId);
                  const percentage = (item.total / transactionState.totalIncome) * 100;
                  
                  return (
                    <View key={item.categoryId} style={styles.breakdownItem}>
                      <View style={styles.breakdownLeft}>
                        <View style={[
                          styles.breakdownColor,
                          { backgroundColor: category?.color || '#ccc' }
                        ]} />
                        <Text style={styles.breakdownName}>
                          {category?.name || 'Unknown'}
                        </Text>
                      </View>
                      <View style={styles.breakdownRight}>
                        <Text style={styles.breakdownAmount}>
                          {CurrencyFormatter.format(item.total)}
                        </Text>
                        <Text style={styles.breakdownPercentage}>
                          {percentage.toFixed(1)}%
                        </Text>
                      </View>
                    </View>
                  );
                })}
              </>
            )}
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  content: {
    flex: 1,
  },
  periodSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    marginHorizontal: 20,
    marginTop: 16,
  },
  summaryCard: {
    marginHorizontal: 20,
    marginVertical: 16,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  periodTitle: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 20,
    color: '#1A1D29',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  income: {
    color: '#10B981',
  },
  expense: {
    color: '#EF4444',
  },
  balanceContainer: {
    alignItems: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#F1F3F4',
  },
  balanceValue: {
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: -1,
  },
  chartCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 20,
    color: '#1A1D29',
  },
  breakdownCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  breakdownSubtitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 12,
  },
  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  breakdownLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  breakdownColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  breakdownName: {
    fontSize: 16,
    color: '#1A1D29',
    flex: 1,
    fontWeight: '500',
  },
  breakdownRight: {
    alignItems: 'flex-end',
  },
  breakdownAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1D29',
    marginBottom: 2,
  },
  breakdownPercentage: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
});

export default ReportsScreen;
