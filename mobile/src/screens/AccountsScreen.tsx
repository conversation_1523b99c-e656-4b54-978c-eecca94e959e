import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { Appbar, FAB, Card, Button, SegmentedButtons } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

import { useAccounts } from '../providers';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { useGuestGuard } from '../hooks/useGuestGuard';
import { Account, AccountInput } from '../models';
import { CurrencyFormatter } from '../utils';

const ACCOUNT_TYPES = [
  { value: 'checking', label: 'Vadesiz', icon: 'account-balance' },
  { value: 'savings', label: 'Vadeli', icon: 'savings' },
  { value: 'credit', label: '<PERSON><PERSON><PERSON>', icon: 'credit-card' },
  { value: 'cash', label: 'Nakit', icon: 'money' },
  { value: 'investment', label: 'Yatırım', icon: 'trending-up' },
];

const ACCOUNT_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
];

const AccountsScreen: React.FC = () => {
  const { state, loadAccounts, createAccount, updateAccount, deleteAccount, getTotalBalance } = useAccounts();
  const { requireAuth } = useAuthGuard();
  const { requireAuth: requireAuthGuest } = useGuestGuard();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    type: 'checking' as 'checking' | 'savings' | 'credit' | 'cash' | 'investment',
    balance: '',
    color: '#FF6B6B',
  });

  useEffect(() => {
    loadAccounts();
  }, []);

  const handleAddAccount = () => {
    requireAuthGuest(() => {
      setEditingAccount(null);
      setFormData({
        name: '',
        type: 'checking',
        balance: '',
        color: '#FF6B6B',

      });
      setModalVisible(true);
    }, {
      title: 'Hesap Eklemek İçin Giriş Yapın',
      message: 'Yeni hesap eklemek için hesabınıza giriş yapmanız gerekiyor.',
    });
  };

  const handleAccountPress = (account: Account) => {
    requireAuth(() => {
      setEditingAccount(account);
      setFormData({
        name: account.name,
        type: account.type,
        balance: account.balance.toString(),
        color: account.color || '#FF6B6B',

      });
      setModalVisible(true);
    }, {
      title: 'Hesap Düzenlemek İçin Giriş Yapın',
      message: 'Hesap düzenlemek için hesabınıza giriş yapmanız gerekiyor.',
    });
  };

  const handleDeleteAccount = (account: Account) => {
    requireAuth(() => {
      Alert.alert(
        'Hesap Sil',
        `"${account.name}" hesabını silmek istediğinizden emin misiniz?`,
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Sil',
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteAccount(account.id!);
                Alert.alert('Başarılı', 'Hesap silindi.');
              } catch (error) {
                Alert.alert('Hata', 'Hesap silinirken bir hata oluştu.');
              }
            },
          },
        ]
      );
    });
  };

  const handleSaveAccount = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Hesap adı boş olamaz.');
      return;
    }

    const balance = parseFloat(formData.balance) || 0;

    try {
      const accountData: AccountInput = {
        name: formData.name.trim(),
        type: formData.type,
        balance,
        color: formData.color,
        currency: 'TRY',
        icon: 'account-balance',
      };

      if (editingAccount) {
        await updateAccount(editingAccount.id!, accountData);
        Alert.alert('Başarılı', 'Hesap güncellendi.');
      } else {
        await createAccount(accountData);
        Alert.alert('Başarılı', 'Hesap eklendi.');
      }

      setModalVisible(false);
      setEditingAccount(null);
    } catch (error) {
      Alert.alert('Hata', 'Hesap kaydedilirken bir hata oluştu.');
    }
  };

  const getAccountTypeInfo = (type: string) => {
    return ACCOUNT_TYPES.find(t => t.value === type) || ACCOUNT_TYPES[0];
  };

  const renderAccount = ({ item }: { item: Account }) => {
    const typeInfo = getAccountTypeInfo(item.type);
    
    return (
      <TouchableOpacity onPress={() => handleAccountPress(item)}>
        <Card style={styles.accountCard}>
          <Card.Content>
            <View style={styles.accountContent}>
              <View style={styles.accountLeft}>
                <View style={[
                  styles.accountIcon,
                  { backgroundColor: item.color || '#FF6B6B' }
                ]}>
                  <MaterialIcons 
                    name={typeInfo.icon as any} 
                    size={24} 
                    color="white" 
                  />
                </View>
                <View style={styles.accountInfo}>
                  <Text style={styles.accountName}>{item.name}</Text>
                  <Text style={styles.accountType}>{typeInfo.label}</Text>

                </View>
              </View>
              <View style={styles.accountRight}>
                <Text style={[
                  styles.accountBalance,
                  { color: item.balance >= 0 ? '#2ECC71' : '#E74C3C' }
                ]}>
                  {CurrencyFormatter.format(item.balance)}
                </Text>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => handleDeleteAccount(item)}
                >
                  <MaterialIcons name="delete" size={20} color="#FF6B6B" />
                </TouchableOpacity>
              </View>
            </View>
          </Card.Content>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderColorSelector = () => (
    <View style={styles.colorSelector}>
      <Text style={styles.selectorTitle}>Renk Seçin:</Text>
      <View style={styles.colorGrid}>
        {ACCOUNT_COLORS.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              formData.color === color && styles.selectedColorOption,
            ]}
            onPress={() => setFormData({ ...formData, color })}
          />
        ))}
      </View>
    </View>
  );

  const totalBalance = getTotalBalance();

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Total Balance Card */}
        <Card style={styles.totalCard}>
          <Card.Content>
            <View style={styles.totalContent}>
              <Text style={styles.totalLabel}>Toplam Bakiye</Text>
              <Text style={[
                styles.totalAmount,
                { color: totalBalance >= 0 ? '#2ECC71' : '#E74C3C' }
              ]}>
                {CurrencyFormatter.format(totalBalance)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {state.loading ? (
          <View style={styles.loadingContainer}>
            <Text>Hesaplar yükleniyor...</Text>
          </View>
        ) : (
          <FlatList
            data={state.accounts}
            renderItem={renderAccount}
            keyExtractor={(item) => item.id!}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={handleAddAccount}
      />

      {/* Account Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {editingAccount ? 'Hesap Düzenle' : 'Yeni Hesap'}
            </Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Hesap Adı:</Text>
              <TextInput
                style={styles.textInput}
                value={formData.name}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                placeholder="Hesap adını girin"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Hesap Türü:</Text>
              <View style={styles.typeSelector}>
                {ACCOUNT_TYPES.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.typeOption,
                      formData.type === type.value && styles.selectedTypeOption,
                    ]}
                    onPress={() => setFormData({ ...formData, type: type.value as 'checking' | 'savings' | 'credit' | 'cash' | 'investment' })}
                  >
                    <MaterialIcons name={type.icon as any} size={20} color="#333" />
                    <Text style={styles.typeLabel}>{type.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Başlangıç Bakiyesi:</Text>
              <TextInput
                style={styles.textInput}
                value={formData.balance}
                onChangeText={(text) => setFormData({ ...formData, balance: text })}
                placeholder="0.00"
                keyboardType="numeric"
              />
            </View>



            {renderColorSelector()}

            <View style={styles.previewContainer}>
              <Text style={styles.selectorTitle}>Önizleme:</Text>
              <View style={styles.accountPreview}>
                <View style={[styles.accountIcon, { backgroundColor: formData.color }]}>
                  <MaterialIcons 
                    name={getAccountTypeInfo(formData.type).icon as any} 
                    size={24} 
                    color="white" 
                  />
                </View>
                <View>
                  <Text style={styles.previewText}>{formData.name || 'Hesap Adı'}</Text>
                  <Text style={styles.previewSubtext}>
                    {getAccountTypeInfo(formData.type).label}
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setModalVisible(false)}
              style={styles.cancelButton}
            >
              İptal
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveAccount}
              style={styles.saveButton}
            >
              {editingAccount ? 'Güncelle' : 'Kaydet'}
            </Button>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  totalCard: {
    marginBottom: 24,
    backgroundColor: '#6366F1',
    borderRadius: 24,
    elevation: 8,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
  },
  totalContent: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
  },
  totalLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: '#fff',
    letterSpacing: -1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 100,
  },
  accountCard: {
    marginBottom: 16,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  accountContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountIcon: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1D29',
    marginBottom: 4,
  },
  accountType: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    marginBottom: 2,
  },
  accountDescription: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '400',
  },
  accountRight: {
    alignItems: 'flex-end',
  },
  accountBalance: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  deleteButton: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#FEF2F2',
  },
  fab: {
    position: 'absolute',
    margin: 20,
    right: 0,
    bottom: 0,
    backgroundColor: '#6366F1',
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1D29',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  textInput: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
    fontWeight: '500',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    backgroundColor: '#F8F9FA',
    minWidth: 120,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedTypeOption: {
    backgroundColor: '#6366F1',
    borderColor: '#4F46E5',
  },
  typeLabel: {
    marginLeft: 12,
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  colorSelector: {
    marginBottom: 24,
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 48,
    height: 48,
    borderRadius: 16,
    marginRight: 12,
    marginBottom: 12,
    borderWidth: 3,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#1A1D29',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  previewContainer: {
    marginBottom: 24,
  },
  accountPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  previewText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1D29',
    marginLeft: 16,
  },
  previewSubtext: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 16,
    marginTop: 4,
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F1F3F4',
  },
  cancelButton: {
    flex: 1,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6366F1',
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default AccountsScreen;
