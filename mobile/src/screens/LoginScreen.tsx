import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  Dimensions,
  Image,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// import { LinearGradient } from 'expo-linear-gradient';

import { useAuth } from '../providers/AuthProvider';
import { RootStackParamList } from '../navigation/AppNavigator';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { theme } from '../theme';

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const { width, height } = Dimensions.get('window');

const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { state, login, clearError, continueAsGuest } = useAuth();

  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });

  useEffect(() => {
    if (state.isAuthenticated) {
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' }],
      });
    }
  }, [state.isAuthenticated, navigation]);

  useEffect(() => {
    if (state.error) {
      Alert.alert('Hata', state.error, [
        { text: 'Tamam', onPress: clearError }
      ]);
    }
  }, [state.error, clearError]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogin = async () => {
    if (!formData.username.trim() || !formData.password.trim()) {
      Alert.alert('Hata', 'Lütfen tüm alanları doldurun.');
      return;
    }

    try {
      await login({
        username: formData.username.trim(),
        password: formData.password,
      });
    } catch (error) {
      // Error is handled by the provider and shown via useEffect
    }
  };

  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  const handleContinueAsGuest = () => {
    continueAsGuest();
    navigation.reset({
      index: 0,
      routes: [{ name: 'Main' }],
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={theme.colors.primary[600]} />

      <View style={styles.gradient}>
        <KeyboardAvoidingView
          style={styles.keyboardView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <View style={styles.header}>
              <Image
                source={require('../../assets/Butce360Logo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.title}>Butce360</Text>
              <Text style={styles.subtitle}>Finansal yönetiminizi kolaylaştırın</Text>
            </View>

            {/* Login Form */}
            <Card style={styles.formCard}>
              <Text style={styles.formTitle}>Giriş Yap</Text>

              <Input
                label="Kullanıcı Adı"
                value={formData.username}
                onChangeText={(value) => handleInputChange('username', value)}
                leftIcon="person"
                placeholder="Kullanıcı adınızı girin"
                autoCapitalize="none"
                autoCorrect={false}
                required
              />

              <Input
                label="Şifre"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                leftIcon="lock"
                placeholder="Şifrenizi girin"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                required
              />

              <Button
                title={state.loading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
                onPress={handleLogin}
                loading={state.loading}
                disabled={state.loading}
                fullWidth
                style={styles.loginButton}
              />

              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>veya</Text>
                <View style={styles.dividerLine} />
              </View>

              <Button
                title="Hesap Oluştur"
                onPress={navigateToRegister}
                variant="outline"
                icon="person-add"
                disabled={state.loading}
                fullWidth
              />

              <View style={styles.guestSection}>
                <Text style={styles.guestText}>Sadece göz atmak mı istiyorsunuz?</Text>
                <Button
                  title="Ziyaretçi olarak devam et"
                  onPress={handleContinueAsGuest}
                  variant="ghost"
                  icon="visibility"
                  disabled={state.loading}
                  fullWidth
                  style={styles.guestButton}
                />
              </View>
            </Card>

            {/* Features */}
            <View style={styles.features}>
              <View style={styles.feature}>
                <View style={styles.featureIcon}>
                  <MaterialIcons name="security" size={20} color={theme.colors.text.inverse} />
                </View>
                <Text style={styles.featureText}>Güvenli</Text>
              </View>
              <View style={styles.feature}>
                <View style={styles.featureIcon}>
                  <MaterialIcons name="trending-up" size={20} color={theme.colors.text.inverse} />
                </View>
                <Text style={styles.featureText}>Raporlar</Text>
              </View>
              <View style={styles.feature}>
                <View style={styles.featureIcon}>
                  <MaterialIcons name="cloud-sync" size={20} color={theme.colors.text.inverse} />
                </View>
                <Text style={styles.featureText}>Senkronizasyon</Text>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    backgroundColor: '#6366F1',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[8],
  },
  header: {
    alignItems: 'center',
    marginTop: theme.spacing[5],
    marginBottom: theme.spacing[5],
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 16,
    marginBottom: theme.spacing[8],
    marginTop: theme.spacing[4],
  },
  title: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: '800',
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing[2],
    textAlign: 'center',
  },
  subtitle: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.text.inverse,
    textAlign: 'center',
    opacity: 0.9,
  },
  formCard: {
    marginBottom: theme.spacing[8],
    ...theme.shadows.xl,
  },
  formTitle: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing[6],
  },
  loginButton: {
    marginTop: theme.spacing[2],
    marginBottom: theme.spacing[6],
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.spacing[6],
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.border.primary,
  },
  dividerText: {
    marginHorizontal: theme.spacing[4],
    color: theme.colors.text.tertiary,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: '500',
  },
  features: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: theme.spacing[8],
    marginTop: theme.spacing[4],
  },
  feature: {
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing[2],
  },
  featureText: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.text.inverse,
    textAlign: 'center',
    fontWeight: '500',
    opacity: 0.9,
  },
  guestSection: {
    marginTop: theme.spacing[4],
    paddingTop: theme.spacing[4],
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    alignItems: 'center',
  },
  guestText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing[2],
  },
  guestButton: {
    marginTop: theme.spacing[1],
  },
});

export default LoginScreen;
