import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {
  TextInput,
  Button,
  SegmentedButtons,
  Card,
  Menu,
  Divider,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTransactions, useCategories, useAccounts, useAuth } from '../providers';
import { TransactionInput, Transaction } from '../models';
import { DateFormatter, CurrencyFormatter } from '../utils';
import { RootStackParamList } from '../navigation/AppNavigator';
import { DatePickerButton } from '../components/ui';

type AddEditTransactionScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'AddEditTransaction'
>;
type AddEditTransactionScreenRouteProp = RouteProp<
  RootStackParamList,
  'AddEditTransaction'
>;

const AddEditTransactionScreen: React.FC = () => {
  const navigation = useNavigation<AddEditTransactionScreenNavigationProp>();
  const route = useRoute<AddEditTransactionScreenRouteProp>();
  const { transactionId } = route.params || {};

  const { state: transactionState, createTransaction, updateTransaction } = useTransactions();
  const { state: categoryState } = useCategories();
  const { state: accountState } = useAccounts();
  const { state: authState } = useAuth();

  const [formData, setFormData] = useState<TransactionInput>({
    title: '',
    type: 'expense',
    amount: 0,
    currency: 'TRY',
    categoryId: '',
    paymentMethod: 'cash',
    accountId: '',
    note: '',
    transactionDate: new Date(),
    location: '',
  });

  const [loading, setLoading] = useState(false);
  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [accountMenuVisible, setAccountMenuVisible] = useState(false);
  const [paymentMethodMenuVisible, setPaymentMethodMenuVisible] = useState(false);

  const isEditing = !!transactionId;
  const existingTransaction = transactionState.transactions.find(t => t.id === transactionId);

  // Redirect to login if guest tries to access this screen
  useEffect(() => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'İşlem eklemek için giriş yapmanız gerekiyor.',
        [
          {
            text: 'Giriş Yap',
            onPress: () => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            },
          },
          {
            text: 'İptal',
            onPress: () => navigation.goBack(),
            style: 'cancel',
          },
        ]
      );
      return;
    }
  }, [authState.isGuest, navigation]);

  useEffect(() => {
    if (isEditing && existingTransaction) {
      setFormData({
        title: existingTransaction.title,
        type: existingTransaction.type,
        amount: existingTransaction.amount,
        currency: existingTransaction.currency,
        categoryId: existingTransaction.categoryId,
        paymentMethod: existingTransaction.paymentMethod,
        accountId: existingTransaction.accountId,
        note: existingTransaction.note || '',
        transactionDate: existingTransaction.transactionDate,
        location: existingTransaction.location || '',
      });
    } else {
      // Set default values for new transaction
      const defaultAccount = accountState.accounts.find(a => a.isDefault);
      const defaultCategory = categoryState.categories.find(c => c.type === 'expense');
      
      setFormData(prev => ({
        ...prev,
        accountId: defaultAccount?.id || '',
        categoryId: defaultCategory?.id || '',
      }));
    }
  }, [isEditing, existingTransaction, accountState.accounts, categoryState.categories]);

  const handleSave = async () => {
    if (!formData.title.trim()) {
      Alert.alert('Hata', 'İşlem başlığı gereklidir.');
      return;
    }

    if (formData.amount <= 0) {
      Alert.alert('Hata', 'Geçerli bir tutar giriniz.');
      return;
    }

    if (!formData.categoryId) {
      Alert.alert('Hata', 'Kategori seçiniz.');
      return;
    }

    if (!formData.accountId) {
      Alert.alert('Hata', 'Hesap seçiniz.');
      return;
    }

    setLoading(true);

    try {
      if (isEditing && transactionId) {
        await updateTransaction(transactionId, formData);
        Alert.alert('Başarılı', 'İşlem güncellendi.');
      } else {
        await createTransaction(formData);
        Alert.alert('Başarılı', 'İşlem eklendi.');
      }
      navigation.goBack();
    } catch (error) {
      Alert.alert('Hata', 'İşlem kaydedilirken bir hata oluştu.');
      console.error('Error saving transaction:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCategories = categoryState.categories.filter(c => c.type === formData.type);
  const selectedCategory = categoryState.categories.find(c => c.id === formData.categoryId);
  const selectedAccount = accountState.accounts.find(a => a.id === formData.accountId);

  const paymentMethods = [
    { value: 'cash', label: 'Nakit' },
    { value: 'card', label: 'Kart' },
    { value: 'bank_transfer', label: 'Havale' },
    { value: 'check', label: 'Çek' },
    { value: 'other', label: 'Diğer' },
  ];

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Card style={styles.card}>
          <Card.Content>
            {/* Transaction Type */}
            <Text style={styles.label}>İşlem Türü</Text>
            <SegmentedButtons
              value={formData.type}
              onValueChange={(value) => {
                setFormData(prev => ({
                  ...prev,
                  type: value as 'income' | 'expense',
                  categoryId: '' // Reset category when type changes
                }));
              }}
              buttons={[
                {
                  value: 'expense',
                  label: '💸 Gider',
                  icon: 'trending-down',
                  style: { backgroundColor: formData.type === 'expense' ? '#6366F1' : 'transparent' }
                },
                {
                  value: 'income',
                  label: '💰 Gelir',
                  icon: 'trending-up',
                  style: { backgroundColor: formData.type === 'income' ? '#6366F1' : 'transparent' }
                },
              ]}
              style={styles.segmentedButtons}
              theme={{
                colors: {
                  secondaryContainer: '#6366F1',
                  onSecondaryContainer: '#FFFFFF',
                  outline: '#E5E7EB',
                }
              }}
            />

            {/* Title */}
            <TextInput
              label="İşlem Başlığı"
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              style={styles.input}
              mode="outlined"
            />

            {/* Amount */}
            <TextInput
              label="Tutar"
              value={formData.amount.toString()}
              onChangeText={(text) => {
                const amount = CurrencyFormatter.parse(text);
                setFormData(prev => ({ ...prev, amount }));
              }}
              style={styles.input}
              mode="outlined"
              keyboardType="numeric"
              right={<TextInput.Affix text="₺" />}
            />

            {/* Category */}
            <Text style={styles.label}>Kategori</Text>
            <Menu
              visible={categoryMenuVisible}
              onDismiss={() => setCategoryMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => setCategoryMenuVisible(true)}
                >
                  <View style={styles.menuButtonContent}>
                    {selectedCategory && (
                      <View style={[
                        styles.categoryIcon,
                        { backgroundColor: selectedCategory.color }
                      ]}>
                        <MaterialIcons 
                          name={selectedCategory.icon as any} 
                          size={16} 
                          color="white" 
                        />
                      </View>
                    )}
                    <Text style={styles.menuButtonText}>
                      {selectedCategory?.name || 'Kategori Seçin'}
                    </Text>
                    <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
                  </View>
                </TouchableOpacity>
              }
            >
              {filteredCategories.map((category) => (
                <Menu.Item
                  key={category.id}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, categoryId: category.id! }));
                    setCategoryMenuVisible(false);
                  }}
                  title={category.name}
                  leadingIcon={() => (
                    <View style={[
                      styles.categoryIcon,
                      { backgroundColor: category.color }
                    ]}>
                      <MaterialIcons 
                        name={category.icon as any} 
                        size={16} 
                        color="white" 
                      />
                    </View>
                  )}
                />
              ))}
            </Menu>

            {/* Account */}
            <Text style={styles.label}>Hesap</Text>
            <Menu
              visible={accountMenuVisible}
              onDismiss={() => setAccountMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => setAccountMenuVisible(true)}
                >
                  <View style={styles.menuButtonContent}>
                    <Text style={styles.menuButtonText}>
                      {selectedAccount?.name || 'Hesap Seçin'}
                    </Text>
                    <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
                  </View>
                </TouchableOpacity>
              }
            >
              {accountState.accounts.map((account) => (
                <Menu.Item
                  key={account.id}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, accountId: account.id! }));
                    setAccountMenuVisible(false);
                  }}
                  title={account.name}
                />
              ))}
            </Menu>

            {/* Payment Method */}
            <Text style={styles.label}>Ödeme Yöntemi</Text>
            <Menu
              visible={paymentMethodMenuVisible}
              onDismiss={() => setPaymentMethodMenuVisible(false)}
              anchor={
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={() => setPaymentMethodMenuVisible(true)}
                >
                  <View style={styles.menuButtonContent}>
                    <Text style={styles.menuButtonText}>
                      {paymentMethods.find(p => p.value === formData.paymentMethod)?.label || 'Ödeme Yöntemi'}
                    </Text>
                    <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
                  </View>
                </TouchableOpacity>
              }
            >
              {paymentMethods.map((method) => (
                <Menu.Item
                  key={method.value}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, paymentMethod: method.value }));
                    setPaymentMethodMenuVisible(false);
                  }}
                  title={method.label}
                />
              ))}
            </Menu>

            {/* Date */}
            <DatePickerButton
              label="Tarih"
              date={formData.transactionDate}
              onDateChange={(date) => setFormData(prev => ({ ...prev, transactionDate: date }))}
              mode="date"
              style={{ marginBottom: 16 }}
            />

            {/* Note */}
            <TextInput
              label="Not (İsteğe bağlı)"
              value={formData.note}
              onChangeText={(text) => setFormData(prev => ({ ...prev, note: text }))}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={3}
            />

            {/* Location */}
            <TextInput
              label="Konum (İsteğe bağlı)"
              value={formData.location}
              onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
              style={styles.input}
              mode="outlined"
            />
          </Card.Content>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSave}
            loading={loading}
            disabled={loading}
            style={styles.saveButton}
          >
            {isEditing ? 'Güncelle' : 'Kaydet'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  scrollView: {
    flex: 1,
  },
  card: {
    marginHorizontal: 20,
    marginVertical: 16,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 20,
    color: '#374151',
  },
  input: {
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
  },
  segmentedButtons: {
    marginBottom: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    padding: 4,
  },
  menuButton: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
  },
  menuButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButtonText: {
    fontSize: 16,
    color: '#1A1D29',
    flex: 1,
    fontWeight: '500',
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
  },
  dateText: {
    fontSize: 16,
    color: '#1A1D29',
    fontWeight: '500',
  },
  buttonContainer: {
    padding: 20,
  },
  saveButton: {
    backgroundColor: '#6366F1',
    borderRadius: 16,
    paddingVertical: 4,
    elevation: 2,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default AddEditTransactionScreen;
