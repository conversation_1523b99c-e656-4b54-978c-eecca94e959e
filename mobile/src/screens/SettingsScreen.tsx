import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Modal,
  Share,
} from 'react-native';
import { Appbar, Card, List, Divider, Button, RadioButton } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { RootStackParamList } from '../navigation/AppNavigator';

type SettingsScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { state: authState, logout } = useAuth();
  const { state: themeState, setColorScheme } = useTheme();
  const { requireAuth } = useAuthGuard();

  // Settings state
  const [notifications, setNotifications] = useState(true);
  const [biometric, setBiometric] = useState(false);
  const [pinEnabled, setPinEnabled] = useState(false);
  const [currency, setCurrency] = useState('TRY');
  
  // Modal states
  const [currencyModalVisible, setCurrencyModalVisible] = useState(false);
  const [themeModalVisible, setThemeModalVisible] = useState(false);

  const currencies = [
    { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
    { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' },
  ];

  const themes = [
    { value: 'light', name: 'Açık Tema' },
    { value: 'dark', name: 'Koyu Tema' },
    { value: 'auto', name: 'Sistem Ayarı' },
  ];

  const getCurrentThemeName = () => {
    if (themeState.colorScheme === 'light') return 'Açık Tema';
    if (themeState.colorScheme === 'dark') return 'Koyu Tema';
    return 'Sistem Ayarı';
  };

  const handleExportData = () => {
    requireAuth(async () => {
      try {
        Alert.alert(
          'Veri Dışa Aktarma',
          'Verilerinizi CSV formatında dışa aktarmak istiyor musunuz?',
          [
            { text: 'İptal', style: 'cancel' },
            {
              text: 'Dışa Aktar',
              onPress: async () => {
                // Mock data export
                const csvData = 'Tarih,Açıklama,Kategori,Tutar,Tür\n2024-01-01,Örnek İşlem,Yemek,50.00,Gider\n';
                const fileUri = FileSystem.documentDirectory + 'finance_data.csv';
                
                await FileSystem.writeAsStringAsync(fileUri, csvData);
                
                if (await Sharing.isAvailableAsync()) {
                  await Sharing.shareAsync(fileUri);
                } else {
                  Alert.alert('Başarılı', 'Veriler dışa aktarıldı.');
                }
              },
            },
          ]
        );
      } catch (error) {
        Alert.alert('Hata', 'Veri dışa aktarılırken bir hata oluştu.');
      }
    }, {
      title: 'Veri Dışa Aktarmak İçin Giriş Yapın',
      message: 'Verilerinizi dışa aktarmak için hesabınıza giriş yapmanız gerekiyor.',
    });
  };

  const handleImportData = () => {
    requireAuth(() => {
      Alert.alert(
        'Veri İçe Aktarma',
        'Bu özellik yakında eklenecek. CSV dosyalarından veri içe aktarabileceksiniz.',
        [{ text: 'Tamam' }]
      );
    });
  };

  const handleBackupData = () => {
    requireAuth(() => {
      Alert.alert(
        'Veri Yedekleme',
        'Verileriniz otomatik olarak bulut depolamaya yedeklenir. Manuel yedekleme özelliği yakında eklenecek.',
        [{ text: 'Tamam' }]
      );
    });
  };

  const handleClearData = () => {
    requireAuth(() => {
      Alert.alert(
        'Tüm Verileri Sil',
        'Bu işlem geri alınamaz. Tüm işlemleriniz, kategorileriniz ve hesaplarınız silinecek.',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Sil',
            style: 'destructive',
            onPress: () => {
              Alert.alert('Bilgi', 'Veri silme özelliği yakında eklenecek.');
            },
          },
        ]
      );
    });
  };

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const handleShareApp = async () => {
    try {
      await Share.share({
        message: 'Butce360 - Kişisel finans yönetimi uygulaması. App Store\'dan indirin!',
        title: 'Butce360',
      });
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const handleRateApp = () => {
    Alert.alert(
      'Uygulamayı Değerlendir',
      'Uygulamayı beğendiyseniz App Store\'da değerlendirmeyi unutmayın!',
      [
        { text: 'Daha Sonra' },
        { text: 'Değerlendir', onPress: () => {
          // Open App Store rating
          Alert.alert('Bilgi', 'App Store açılacak...');
        }},
      ]
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Info */}
        {authState.isAuthenticated && (
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.userInfo}>
                <View style={styles.avatar}>
                  <MaterialIcons name="person" size={32} color="#fff" />
                </View>
                <View style={styles.userDetails}>
                  <Text style={styles.userName}>{authState.user?.name || 'Kullanıcı'}</Text>
                  <Text style={styles.userEmail}>{authState.user?.email || ''}</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Guest Mode Info */}
        {authState.isGuest && (
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.guestInfo}>
                <MaterialIcons name="visibility" size={24} color="#666" />
                <Text style={styles.guestText}>Misafir modunda kullanıyorsunuz</Text>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('Login')}
                  style={styles.loginButton}
                >
                  Giriş Yap
                </Button>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Data Management */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Veri Yönetimi</Text>
            
            <List.Item
              title="Veri Dışa Aktar"
              description="CSV formatında dışa aktar"
              left={(props) => <List.Icon {...props} icon="download" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleExportData}
            />
            
            <Divider />
            
            <List.Item
              title="Veri İçe Aktar"
              description="CSV dosyasından içe aktar"
              left={(props) => <List.Icon {...props} icon="upload" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleImportData}
            />
            
            <Divider />
            
            <List.Item
              title="Veri Yedekle"
              description="Bulut depolamaya yedekle"
              left={(props) => <List.Icon {...props} icon="cloud-upload" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleBackupData}
            />
            
            <Divider />
            
            <List.Item
              title="Tüm Verileri Sil"
              description="Dikkatli kullanın!"
              left={(props) => <List.Icon {...props} icon="delete-forever" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleClearData}
            />
          </Card.Content>
        </Card>

        {/* App Settings */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Uygulama Ayarları</Text>
            
            <List.Item
              title="Para Birimi"
              description={currencies.find(c => c.code === currency)?.name}
              left={(props) => <List.Icon {...props} icon="currency-try" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => setCurrencyModalVisible(true)}
            />
            
            <Divider />
            
            <List.Item
              title="Bildirimler"
              description="Hatırlatma bildirimleri"
              left={(props) => <List.Icon {...props} icon="bell" />}
              right={() => (
                <Switch
                  value={notifications}
                  onValueChange={setNotifications}
                />
              )}
            />
            
            <Divider />
            
            <List.Item
              title="Tema"
              description={getCurrentThemeName()}
              left={(props) => <List.Icon {...props} icon="palette" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => setThemeModalVisible(true)}
            />
          </Card.Content>
        </Card>

        {/* Security */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Güvenlik</Text>
            
            <List.Item
              title="PIN Kodu"
              description="Uygulamayı PIN ile koruyun"
              left={(props) => <List.Icon {...props} icon="lock" />}
              right={() => (
                <Switch
                  value={pinEnabled}
                  onValueChange={setPinEnabled}
                />
              )}
            />
            
            <Divider />
            
            <List.Item
              title="Biyometrik Kimlik Doğrulama"
              description="Parmak izi veya yüz tanıma"
              left={(props) => <List.Icon {...props} icon="fingerprint" />}
              right={() => (
                <Switch
                  value={biometric}
                  onValueChange={setBiometric}
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* About */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Hakkında</Text>
            
            <List.Item
              title="Uygulamayı Paylaş"
              description="Arkadaşlarınızla paylaşın"
              left={(props) => <List.Icon {...props} icon="share" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleShareApp}
            />
            
            <Divider />
            
            <List.Item
              title="Uygulamayı Değerlendir"
              description="App Store'da değerlendirin"
              left={(props) => <List.Icon {...props} icon="star" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleRateApp}
            />
            
            <Divider />
            
            <List.Item
              title="Sürüm"
              description="1.0.0"
              left={(props) => <List.Icon {...props} icon="information" />}
            />
          </Card.Content>
        </Card>

        {/* Account Actions */}
        {authState.isAuthenticated && (
          <Card style={styles.card}>
            <Card.Content>
              <Button
                mode="outlined"
                onPress={handleLogout}
                style={styles.logoutButton}
                textColor="#E74C3C"
              >
                Çıkış Yap
              </Button>
            </Card.Content>
          </Card>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Currency Modal */}
      <Modal
        visible={currencyModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Para Birimi Seçin</Text>
            <TouchableOpacity onPress={() => setCurrencyModalVisible(false)}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            <RadioButton.Group
              onValueChange={setCurrency}
              value={currency}
            >
              {currencies.map((curr) => (
                <TouchableOpacity
                  key={curr.code}
                  style={styles.radioItem}
                  onPress={() => setCurrency(curr.code)}
                >
                  <RadioButton value={curr.code} />
                  <View style={styles.radioContent}>
                    <Text style={styles.radioTitle}>{curr.name}</Text>
                    <Text style={styles.radioSubtitle}>{curr.symbol} - {curr.code}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </RadioButton.Group>
          </View>
          
          <View style={styles.modalActions}>
            <Button
              mode="contained"
              onPress={() => setCurrencyModalVisible(false)}
              style={styles.saveButton}
            >
              Kaydet
            </Button>
          </View>
        </View>
      </Modal>

      {/* Theme Modal */}
      <Modal
        visible={themeModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Tema Seçin</Text>
            <TouchableOpacity onPress={() => setThemeModalVisible(false)}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalContent}>
            <RadioButton.Group
              onValueChange={(value) => {
                setColorScheme(value as any);
                setThemeModalVisible(false);
              }}
              value={themeState.colorScheme}
            >
              {themes.map((themeOption) => (
                <TouchableOpacity
                  key={themeOption.value}
                  style={styles.radioItem}
                  onPress={() => {
                    setColorScheme(themeOption.value as any);
                    setThemeModalVisible(false);
                  }}
                >
                  <RadioButton value={themeOption.value} />
                  <Text style={styles.radioTitle}>{themeOption.name}</Text>
                </TouchableOpacity>
              ))}
            </RadioButton.Group>
          </View>

          <View style={styles.modalActions}>
            <Button
              mode="contained"
              onPress={() => setThemeModalVisible(false)}
              style={styles.saveButton}
            >
              Kapat
            </Button>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  guestInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  guestText: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    marginLeft: 12,
  },
  loginButton: {
    backgroundColor: '#2ECC71',
  },
  logoutButton: {
    borderColor: '#E74C3C',
  },
  bottomSpacing: {
    height: 40,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalActions: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  radioContent: {
    marginLeft: 12,
  },
  radioTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  radioSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  saveButton: {
    backgroundColor: '#2ECC71',
  },
});

export default SettingsScreen;
