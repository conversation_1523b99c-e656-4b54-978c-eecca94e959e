import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { Appbar, FAB, Card, SegmentedButtons, Button } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

import { useCategories } from '../providers';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { Category, CategoryInput } from '../models';

const CATEGORY_ICONS = [
  'restaurant', 'local-gas-station', 'shopping-cart', 'home', 'directions-car',
  'medical-services', 'school', 'sports-esports', 'movie', 'flight',
  'hotel', 'phone', 'wifi', 'electric-bolt', 'water-drop',
  'fitness-center', 'pets', 'child-care', 'elderly', 'work',
  'business', 'savings', 'trending-up', 'account-balance', 'credit-card'
];

const CATEGORY_COLORS = [
  '#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B',
  '#10B981', '#06B6D4', '#84CC16', '#F97316', '#6B7280',
  '#14B8A6', '#3B82F6', '#8B5A2B', '#DC2626', '#7C3AED',
  '#059669', '#0EA5E9', '#65A30D', '#EA580C', '#4B5563'
];

const CategoriesScreen: React.FC = () => {
  const { state, loadCategories, createCategory, updateCategory, deleteCategory } = useCategories();
  const { requireAuth } = useAuthGuard();
  const [selectedType, setSelectedType] = useState<'income' | 'expense'>('expense');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    icon: 'restaurant',
    color: '#6366F1',
    type: 'expense' as 'income' | 'expense',
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const filteredCategories = state.categories.filter(c => c.type === selectedType);

  const handleAddCategory = () => {
    requireAuth(() => {
      setEditingCategory(null);
      setFormData({
        name: '',
        icon: 'restaurant',
        color: '#FF6B6B',
        type: selectedType,
      });
      setModalVisible(true);
    }, {
      title: 'Kategori Eklemek İçin Giriş Yapın',
      message: 'Yeni kategori eklemek için hesabınıza giriş yapmanız gerekiyor.',
    });
  };

  const handleCategoryPress = (category: Category) => {
    requireAuth(() => {
      setEditingCategory(category);
      setFormData({
        name: category.name,
        icon: category.icon,
        color: category.color,
        type: category.type,
      });
      setModalVisible(true);
    }, {
      title: 'Kategori Düzenlemek İçin Giriş Yapın',
      message: 'Kategori düzenlemek için hesabınıza giriş yapmanız gerekiyor.',
    });
  };

  const handleDeleteCategory = (category: Category) => {
    requireAuth(() => {
      Alert.alert(
        'Kategori Sil',
        `"${category.name}" kategorisini silmek istediğinizden emin misiniz?`,
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Sil',
            style: 'destructive',
            onPress: async () => {
              try {
                await deleteCategory(category.id!);
                Alert.alert('Başarılı', 'Kategori silindi.');
              } catch (error) {
                Alert.alert('Hata', 'Kategori silinirken bir hata oluştu.');
              }
            },
          },
        ]
      );
    });
  };

  const handleSaveCategory = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Hata', 'Kategori adı boş olamaz.');
      return;
    }

    try {
      const categoryData: CategoryInput = {
        name: formData.name.trim(),
        icon: formData.icon,
        color: formData.color,
        type: formData.type,
      };

      if (editingCategory) {
        await updateCategory(editingCategory.id!, categoryData);
        Alert.alert('Başarılı', 'Kategori güncellendi.');
      } else {
        await createCategory(categoryData);
        Alert.alert('Başarılı', 'Kategori eklendi.');
      }

      setModalVisible(false);
      setEditingCategory(null);
    } catch (error) {
      Alert.alert('Hata', 'Kategori kaydedilirken bir hata oluştu.');
    }
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      onPress={() => handleCategoryPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.categoryCard}>
        <View style={styles.categoryContent}>
          <View style={styles.categoryLeft}>
            <View style={[
              styles.categoryIcon,
              { backgroundColor: item.color || '#6366F1' }
            ]}>
              <MaterialIcons
                name={item.icon as any}
                size={28}
                color="white"
              />
            </View>
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryName}>{item.name}</Text>
              <Text style={styles.categoryType}>
                {item.type === 'income' ? '💰 Gelir Kategorisi' : '💸 Gider Kategorisi'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteCategory(item)}
            activeOpacity={0.7}
          >
            <MaterialIcons name="delete" size={22} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderIconSelector = () => (
    <View style={styles.iconSelector}>
      <Text style={styles.selectorTitle}>İkon Seçin:</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.iconGrid}>
          {CATEGORY_ICONS.map((icon) => (
            <TouchableOpacity
              key={icon}
              style={[
                styles.iconOption,
                formData.icon === icon && styles.selectedIconOption,
              ]}
              onPress={() => setFormData({ ...formData, icon })}
            >
              <MaterialIcons
                name={icon as any}
                size={24}
                color={formData.icon === icon ? "white" : "#6B7280"}
              />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  const renderColorSelector = () => (
    <View style={styles.colorSelector}>
      <Text style={styles.selectorTitle}>Renk Seçin:</Text>
      <View style={styles.colorGrid}>
        {CATEGORY_COLORS.map((color) => (
          <TouchableOpacity
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              formData.color === color && styles.selectedColorOption,
            ]}
            onPress={() => setFormData({ ...formData, color })}
          />
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <SegmentedButtons
          value={selectedType}
          onValueChange={(value) => setSelectedType(value as 'income' | 'expense')}
          buttons={[
            {
              value: 'expense',
              label: '💸 Gider',
              style: { backgroundColor: selectedType === 'expense' ? '#6366F1' : 'transparent' }
            },
            {
              value: 'income',
              label: '💰 Gelir',
              style: { backgroundColor: selectedType === 'income' ? '#6366F1' : 'transparent' }
            },
          ]}
          style={styles.segmentedButtons}
          theme={{
            colors: {
              secondaryContainer: '#6366F1',
              onSecondaryContainer: '#FFFFFF',
              outline: '#E5E7EB',
            }
          }}
        />

        {state.loading ? (
          <View style={styles.loadingContainer}>
            <Text>Kategoriler yükleniyor...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredCategories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id!}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={handleAddCategory}
      />

      {/* Category Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {editingCategory ? 'Kategori Düzenle' : 'Yeni Kategori'}
            </Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Kategori Adı:</Text>
              <TextInput
                style={styles.textInput}
                value={formData.name}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                placeholder="Kategori adını girin"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tür:</Text>
              <SegmentedButtons
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value as 'income' | 'expense' })}
                buttons={[
                  { value: 'expense', label: 'Gider' },
                  { value: 'income', label: 'Gelir' },
                ]}
              />
            </View>

            {renderIconSelector()}
            {renderColorSelector()}

            <View style={styles.previewContainer}>
              <Text style={styles.selectorTitle}>Önizleme:</Text>
              <View style={styles.categoryPreview}>
                <View style={[styles.categoryIcon, { backgroundColor: formData.color }]}>
                  <MaterialIcons name={formData.icon as any} size={24} color="white" />
                </View>
                <Text style={styles.previewText}>{formData.name || 'Kategori Adı'}</Text>
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setModalVisible(false)}
              style={styles.cancelButton}
            >
              İptal
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveCategory}
              style={styles.saveButton}
            >
              {editingCategory ? 'Güncelle' : 'Kaydet'}
            </Button>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  segmentedButtons: {
    marginBottom: 24,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 100,
  },
  categoryCard: {
    marginBottom: 16,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  categoryContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1D29',
    marginBottom: 4,
  },
  categoryType: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  deleteButton: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#FEF2F2',
  },
  fab: {
    position: 'absolute',
    margin: 20,
    right: 0,
    bottom: 0,
    backgroundColor: '#6366F1',
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F4',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1D29',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  textInput: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
    fontWeight: '500',
  },
  iconSelector: {
    marginBottom: 24,
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  iconOption: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedIconOption: {
    backgroundColor: '#6366F1',
    borderColor: '#4F46E5',
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  colorSelector: {
    marginBottom: 24,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 48,
    height: 48,
    borderRadius: 16,
    marginRight: 12,
    marginBottom: 12,
    borderWidth: 3,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#1A1D29',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  previewContainer: {
    marginBottom: 24,
  },
  categoryPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  previewText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1D29',
    marginLeft: 16,
  },
  modalActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F1F3F4',
  },
  cancelButton: {
    flex: 1,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#6366F1',
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default CategoriesScreen;
