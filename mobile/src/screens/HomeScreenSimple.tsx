import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { FAB, Appbar } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { SummaryCard, TransactionCard } from '../components';
import { useTransactions, useCategories, useAccounts } from '../providers';
import { DateFormatter } from '../utils';
import { RootStackParamList } from '../navigation/AppNavigator';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { state: transactionState, loadTransactions } = useTransactions();
  const { loadCategories, initializeDefaultCategories } = useCategories();
  const { loadAccounts, initializeDefaultAccounts } = useAccounts();

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [refreshing, setRefreshing] = useState(false);

  const loadData = useCallback(async () => {
    try {
      const startDate = DateFormatter.getStartOfMonth(selectedDate);
      const endDate = DateFormatter.getEndOfMonth(selectedDate);

      await Promise.all([
        initializeDefaultCategories(),
        initializeDefaultAccounts(),
        loadCategories(),
        loadAccounts(),
        loadTransactions(startDate, endDate),
      ]);
    } catch (error) {
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      console.error('Error loading data:', error);
    }
  }, [selectedDate, loadTransactions, loadCategories, loadAccounts, initializeDefaultCategories, initializeDefaultAccounts]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  const selectPreviousMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setSelectedDate(newDate);
  };

  const selectNextMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setSelectedDate(newDate);
  };

  const handleAddTransaction = () => {
    navigation.navigate('AddEditTransaction', {});
  };

  const handleTransactionPress = (transactionId: string) => {
    navigation.navigate('AddEditTransaction', { transactionId });
  };

  const handleViewAllTransactions = () => {
    navigation.navigate('TransactionList');
  };

  const recentTransactions = transactionState.transactions.slice(0, 5);

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="Butce360" />
      </Appbar.Header>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Month Selector */}
        <View style={styles.monthSelector}>
          <TouchableOpacity onPress={selectPreviousMonth}>
            <MaterialIcons name="chevron-left" size={30} color="#333" />
          </TouchableOpacity>
          
          <Text style={styles.monthText}>
            {DateFormatter.formatMonth(selectedDate)}
          </Text>
          
          <TouchableOpacity onPress={selectNextMonth}>
            <MaterialIcons name="chevron-right" size={30} color="#333" />
          </TouchableOpacity>
        </View>

        {/* Summary Card */}
        <SummaryCard
          income={transactionState.totalIncome}
          expense={transactionState.totalExpense}
          balance={transactionState.balance}
          period={DateFormatter.formatMonth(selectedDate)}
        />

        {/* Recent Transactions */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Son İşlemler</Text>
          <TouchableOpacity onPress={handleViewAllTransactions}>
            <Text style={styles.seeAllText}>Tümünü Gör</Text>
          </TouchableOpacity>
        </View>

        {recentTransactions.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialIcons name="receipt" size={64} color="#ccc" />
            <Text style={styles.emptyText}>
              Henüz işlem yok. İlk işleminizi ekleyin!
            </Text>
          </View>
        ) : (
          recentTransactions.map((transaction) => (
            <TransactionCard
              key={transaction.id}
              transaction={transaction}
              onPress={() => handleTransactionPress(transaction.id!)}
            />
          ))
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="plus"
        label="İşlem Ekle"
        onPress={handleAddTransaction}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  monthSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  monthText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    color: '#2ECC71',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  bottomSpacing: {
    height: 100,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2ECC71',
  },
});

export default HomeScreen;
