import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, Card, Button, Avatar } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useAuth } from '../providers/AuthProvider';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { RootStackParamList } from '../navigation/AppNavigator';

type ProfileScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { state: authState, logout } = useAuth();
  const { requireAuth } = useAuthGuard();

  const [editModalVisible, setEditModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  
  const [editForm, setEditForm] = useState({
    name: authState.user?.name || '',
    email: authState.user?.email || '',
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleEditProfile = () => {
    requireAuth(() => {
      setEditForm({
        name: authState.user?.name || '',
        email: authState.user?.email || '',
      });
      setEditModalVisible(true);
    });
  };

  const handleChangePassword = () => {
    requireAuth(() => {
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setPasswordModalVisible(true);
    });
  };

  const handleSaveProfile = async () => {
    if (!editForm.name.trim()) {
      Alert.alert('Hata', 'İsim boş olamaz.');
      return;
    }

    if (!editForm.email.trim()) {
      Alert.alert('Hata', 'E-posta boş olamaz.');
      return;
    }

    try {
      // TODO: Implement profile update API call
      Alert.alert('Başarılı', 'Profil güncellendi.');
      setEditModalVisible(false);
    } catch (error) {
      Alert.alert('Hata', 'Profil güncellenirken bir hata oluştu.');
    }
  };

  const handleSavePassword = async () => {
    if (!passwordForm.currentPassword) {
      Alert.alert('Hata', 'Mevcut şifrenizi girin.');
      return;
    }

    if (!passwordForm.newPassword) {
      Alert.alert('Hata', 'Yeni şifrenizi girin.');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      Alert.alert('Hata', 'Şifre en az 6 karakter olmalıdır.');
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      Alert.alert('Hata', 'Şifreler eşleşmiyor.');
      return;
    }

    try {
      // TODO: Implement password change API call
      Alert.alert('Başarılı', 'Şifre değiştirildi.');
      setPasswordModalVisible(false);
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      Alert.alert('Hata', 'Şifre değiştirilirken bir hata oluştu.');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            } catch (error) {
              Alert.alert('Hata', 'Çıkış yapılırken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    requireAuth(() => {
      Alert.alert(
        'Hesabı Sil',
        'Bu işlem geri alınamaz. Hesabınız ve tüm verileriniz kalıcı olarak silinecek.',
        [
          { text: 'İptal', style: 'cancel' },
          {
            text: 'Sil',
            style: 'destructive',
            onPress: () => {
              Alert.alert(
                'Son Uyarı',
                'Hesabınızı silmek istediğinizden emin misiniz?',
                [
                  { text: 'İptal', style: 'cancel' },
                  {
                    text: 'Evet, Sil',
                    style: 'destructive',
                    onPress: async () => {
                      try {
                        // TODO: Implement account deletion API call
                        Alert.alert('Bilgi', 'Hesap silme özelliği yakında eklenecek.');
                      } catch (error) {
                        Alert.alert('Hata', 'Hesap silinirken bir hata oluştu.');
                      }
                    },
                  },
                ]
              );
            },
          },
        ]
      );
    });
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!authState.isAuthenticated || authState.isGuest) {
    return (
      <View style={styles.container}>
        <View style={styles.guestContainer}>
          <MaterialIcons name="person-outline" size={80} color="#ccc" />
          <Text style={styles.guestTitle}>
            {authState.isGuest ? 'Ziyaretçi Modundasınız' : 'Profil Görüntülemek İçin Giriş Yapın'}
          </Text>
          <Text style={styles.guestSubtitle}>
            {authState.isGuest
              ? 'Profil özelliklerini kullanmak için hesabınıza giriş yapın veya yeni hesap oluşturun.'
              : 'Profil bilgilerinizi görüntülemek ve düzenlemek için hesabınıza giriş yapmanız gerekiyor.'
            }
          </Text>
          <Button
            mode="contained"
            onPress={() => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            }}
            style={styles.loginButton}
          >
            Giriş Yap
          </Button>
          <Button
            mode="outlined"
            onPress={() => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'Register' }],
              });
            }}
            style={styles.registerButton}
          >
            Hesap Oluştur
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Card style={styles.profileCard}>
          <Card.Content>
            <View style={styles.profileHeader}>
              <Avatar.Text
                size={80}
                label={getInitials(authState.user?.name || 'U')}
                style={styles.avatar}
              />
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>{authState.user?.name}</Text>
                <Text style={styles.profileEmail}>{authState.user?.email}</Text>
                <Text style={styles.profileUsername}>@{authState.user?.username}</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Profile Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Hesap Yönetimi</Text>
            
            <TouchableOpacity style={styles.actionItem} onPress={handleEditProfile}>
              <View style={styles.actionLeft}>
                <MaterialIcons name="edit" size={24} color="#667eea" />
                <Text style={styles.actionTitle}>Profili Düzenle</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionItem} onPress={handleChangePassword}>
              <View style={styles.actionLeft}>
                <MaterialIcons name="lock" size={24} color="#667eea" />
                <Text style={styles.actionTitle}>Şifre Değiştir</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('Main', { screen: 'Settings' })}>
              <View style={styles.actionLeft}>
                <MaterialIcons name="settings" size={24} color="#667eea" />
                <Text style={styles.actionTitle}>Ayarlar</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.logoutItem} onPress={handleLogout}>
              <View style={styles.actionLeft}>
                <MaterialIcons name="logout" size={24} color="#FF6B6B" />
                <Text style={[styles.actionTitle, { color: '#FF6B6B' }]}>Çıkış Yap</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>
          </Card.Content>
        </Card>

        {/* Danger Zone */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Tehlikeli Bölge</Text>
            
            <TouchableOpacity style={styles.dangerItem} onPress={handleDeleteAccount}>
              <View style={styles.actionLeft}>
                <MaterialIcons name="delete-forever" size={24} color="#E74C3C" />
                <Text style={[styles.actionTitle, { color: '#E74C3C' }]}>Hesabı Sil</Text>
              </View>
              <MaterialIcons name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>
          </Card.Content>
        </Card>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Profili Düzenle</Text>
            <TouchableOpacity onPress={() => setEditModalVisible(false)}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>İsim:</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.name}
                onChangeText={(text) => setEditForm({ ...editForm, name: text })}
                placeholder="İsminizi girin"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>E-posta:</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.email}
                onChangeText={(text) => setEditForm({ ...editForm, email: text })}
                placeholder="E-posta adresinizi girin"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
          </View>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setEditModalVisible(false)}
              style={styles.cancelButton}
            >
              İptal
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveProfile}
              style={styles.saveButton}
            >
              Kaydet
            </Button>
          </View>
        </View>
      </Modal>

      {/* Change Password Modal */}
      <Modal
        visible={passwordModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Şifre Değiştir</Text>
            <TouchableOpacity onPress={() => setPasswordModalVisible(false)}>
              <MaterialIcons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Mevcut Şifre:</Text>
              <TextInput
                style={styles.textInput}
                value={passwordForm.currentPassword}
                onChangeText={(text) => setPasswordForm({ ...passwordForm, currentPassword: text })}
                placeholder="Mevcut şifrenizi girin"
                secureTextEntry
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Yeni Şifre:</Text>
              <TextInput
                style={styles.textInput}
                value={passwordForm.newPassword}
                onChangeText={(text) => setPasswordForm({ ...passwordForm, newPassword: text })}
                placeholder="Yeni şifrenizi girin"
                secureTextEntry
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Yeni Şifre (Tekrar):</Text>
              <TextInput
                style={styles.textInput}
                value={passwordForm.confirmPassword}
                onChangeText={(text) => setPasswordForm({ ...passwordForm, confirmPassword: text })}
                placeholder="Yeni şifrenizi tekrar girin"
                secureTextEntry
              />
            </View>
          </View>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setPasswordModalVisible(false)}
              style={styles.cancelButton}
            >
              İptal
            </Button>
            <Button
              mode="contained"
              onPress={handleSavePassword}
              style={styles.saveButton}
            >
              Değiştir
            </Button>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  guestContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  guestTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  guestSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  loginButton: {
    backgroundColor: '#2ECC71',
    marginBottom: 12,
    width: '100%',
  },
  registerButton: {
    borderColor: '#2ECC71',
    width: '100%',
  },
  profileCard: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: '#667eea',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  profileEmail: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  profileUsername: {
    fontSize: 14,
    color: '#999',
    marginTop: 2,
  },
  card: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  actionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  logoutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dangerItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  actionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionTitle: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  bottomSpacing: {
    height: 40,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  modalActions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#2ECC71',
  },
});

export default ProfileScreen;
