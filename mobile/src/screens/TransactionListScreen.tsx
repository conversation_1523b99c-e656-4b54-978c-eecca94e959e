import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import { Appbar, Searchbar, FAB } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { TransactionCard } from '../components';
import { useTransactions } from '../providers';
import { Transaction } from '../models';
import { RootStackParamList } from '../navigation/AppNavigator';

type TransactionListScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'TransactionList'
>;

const TransactionListScreen: React.FC = () => {
  const navigation = useNavigation<TransactionListScreenNavigationProp>();
  const { state, loadTransactions, deleteTransaction } = useTransactions();

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);

  useEffect(() => {
    loadTransactions();
  }, []);

  useEffect(() => {
    // Filter transactions based on search query
    if (searchQuery.trim() === '') {
      setFilteredTransactions(state.transactions);
    } else {
      const filtered = state.transactions.filter(transaction =>
        transaction.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        transaction.note?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        transaction.location?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredTransactions(filtered);
    }
  }, [state.transactions, searchQuery]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactions();
    setRefreshing(false);
  };

  const handleTransactionPress = (transactionId: string) => {
    navigation.navigate('AddEditTransaction', { transactionId });
  };

  const handleTransactionLongPress = (transaction: Transaction) => {
    Alert.alert(
      'İşlem Seçenekleri',
      `"${transaction.title}" işlemi için ne yapmak istiyorsunuz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Düzenle',
          onPress: () => handleTransactionPress(transaction.id!),
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => handleDeleteTransaction(transaction),
        },
      ]
    );
  };

  const handleDeleteTransaction = (transaction: Transaction) => {
    Alert.alert(
      'İşlemi Sil',
      `"${transaction.title}" işlemini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTransaction(transaction.id!);
              Alert.alert('Başarılı', 'İşlem silindi.');
            } catch (error) {
              Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const handleAddTransaction = () => {
    navigation.navigate('AddEditTransaction', {});
  };

  const renderTransaction = ({ item }: { item: Transaction }) => (
    <TransactionCard
      transaction={item}
      onPress={() => handleTransactionPress(item.id!)}
      onLongPress={() => handleTransactionLongPress(item)}
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Searchbar
          placeholder="İşlem ara..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id!}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={handleAddTransaction}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  content: {
    flex: 1,
  },
  searchbar: {
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#F1F3F4',
  },
  listContainer: {
    paddingBottom: 100,
    paddingHorizontal: 20,
  },
  fab: {
    position: 'absolute',
    margin: 20,
    right: 0,
    bottom: 0,
    backgroundColor: '#6366F1',
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#6366F1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default TransactionListScreen;
