import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
} from 'react-native';
import {
  Appbar,
  Card,
  Button,
  ActivityIndicator,
  Checkbox,
  Menu,
  Modal,
  Portal,
  TextInput,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';

import { BankStatementService, BankStatementEntry, SupportedBank } from '../services/BankStatementService';
import { useAccounts } from '../providers/AccountProvider';
import { useCategories } from '../providers/CategoryProvider';
import { CurrencyFormatter, DateFormatter } from '../utils';

const BankStatementScreen: React.FC = () => {
  const { state: accountState, loadAccounts } = useAccounts();
  const { state: categoryState, loadCategories } = useCategories();

  const [loading, setLoading] = useState(false);
  const [parsedEntries, setParsedEntries] = useState<BankStatementEntry[]>([]);
  const [selectedEntries, setSelectedEntries] = useState<Set<number>>(new Set());
  const [selectedBank, setSelectedBank] = useState<SupportedBank>('vakifbank');
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [showBankMenu, setShowBankMenu] = useState(false);
  const [showAccountMenu, setShowAccountMenu] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [filename, setFilename] = useState<string>('');

  const bankStatementService = new BankStatementService();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        loadAccounts(),
        loadCategories(),
      ]);
      
      // Set default account
      const defaultAccount = accountState.accounts.find(a => a.isDefault);
      if (defaultAccount) {
        setSelectedAccount(defaultAccount.id!);
      }
    } catch (error) {
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    }
  };

  const handleFileUpload = async () => {
    if (!selectedAccount) {
      Alert.alert('Hata', 'Lütfen bir hesap seçin.');
      return;
    }

    try {
      setLoading(true);
      
      const result = await bankStatementService.pickDocument();

      if (!result || result.canceled) {
        setLoading(false);
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const file = {
          uri: asset.uri,
          name: asset.name || 'statement.pdf',
          type: asset.mimeType || 'application/pdf',
        };

        const parsedStatement = await bankStatementService.uploadAndParseStatement(
          file,
          selectedBank,
          selectedAccount
        );

        setParsedEntries(parsedStatement.entries);
        setFilename(parsedStatement.filename);
        setSelectedEntries(new Set(parsedStatement.entries.map((_, index) => index)));
        setShowPreview(true);
      }
    } catch (error) {
      Alert.alert('Hata', (error as Error).message || 'Dosya yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (selectedEntries.size === 0) {
      Alert.alert('Hata', 'Lütfen içe aktarılacak işlemleri seçin.');
      return;
    }

    try {
      setLoading(true);
      
      const entriesToImport = parsedEntries
        .filter((_, index) => selectedEntries.has(index))
        .map(entry => ({
          date: entry.date,
          description: entry.description,
          amount: entry.amount,
          type: entry.type,
          category_id: entry.category_id,
          account_id: entry.account_id || selectedAccount,
        }));

      await bankStatementService.importTransactions(entriesToImport);
      
      Alert.alert(
        'Başarılı',
        `${entriesToImport.length} işlem başarıyla içe aktarıldı.`,
        [
          {
            text: 'Tamam',
            onPress: () => {
              setParsedEntries([]);
              setSelectedEntries(new Set());
              setShowPreview(false);
              setFilename('');
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Hata', (error as Error).message || 'İşlemler içe aktarılırken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const toggleEntrySelection = (index: number) => {
    const newSelected = new Set(selectedEntries);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedEntries(newSelected);
  };

  const selectAllEntries = () => {
    setSelectedEntries(new Set(parsedEntries.map((_, index) => index)));
  };

  const deselectAllEntries = () => {
    setSelectedEntries(new Set());
  };

  const renderEntryItem = ({ item, index }: { item: BankStatementEntry; index: number }) => {
    const isSelected = selectedEntries.has(index);
    const formatted = bankStatementService.formatEntryForDisplay(item);

    return (
      <TouchableOpacity
        style={[styles.entryItem, isSelected && styles.entryItemSelected]}
        onPress={() => toggleEntrySelection(index)}
      >
        <View style={styles.entryContent}>
          <Checkbox
            status={isSelected ? 'checked' : 'unchecked'}
            onPress={() => toggleEntrySelection(index)}
          />
          <View style={styles.entryDetails}>
            <Text style={styles.entryDescription} numberOfLines={2}>
              {item.description}
            </Text>
            <Text style={styles.entryDate}>{formatted.displayDate}</Text>
          </View>
          <View style={styles.entryAmount}>
            <Text style={[
              styles.amountText,
              { color: formatted.isIncome ? '#2ECC71' : '#E74C3C' }
            ]}>
              {formatted.isIncome ? '+' : '-'}{formatted.displayAmount}
            </Text>
            <Text style={styles.entryType}>{formatted.displayType}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const supportedBanks = bankStatementService.getSupportedBanks();
  const selectedBankInfo = supportedBanks.find(b => b.value === selectedBank);
  const selectedAccountInfo = accountState.accounts.find(a => a.id === selectedAccount);

  const summary = parsedEntries.length > 0 
    ? bankStatementService.calculateStatementSummary(parsedEntries)
    : null;

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="Ekstre İşleme" />
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {!showPreview ? (
          <View style={styles.uploadSection}>
            <Card style={styles.instructionCard}>
              <Card.Content>
                <View style={styles.instructionHeader}>
                  <MaterialIcons name="cloud-upload" size={48} color="#2ECC71" />
                  <Text style={styles.instructionTitle}>Banka Ekstresi Yükle</Text>
                </View>
                <Text style={styles.instructionText}>
                  PDF formatındaki banka ekstrenizi yükleyerek işlemlerinizi otomatik olarak içe aktarabilirsiniz.
                </Text>
              </Card.Content>
            </Card>

            {/* Bank Selection */}
            <Card style={styles.selectionCard}>
              <Card.Content>
                <Text style={styles.selectionTitle}>Banka Seçimi</Text>
                <Menu
                  visible={showBankMenu}
                  onDismiss={() => setShowBankMenu(false)}
                  anchor={
                    <TouchableOpacity
                      style={styles.menuButton}
                      onPress={() => setShowBankMenu(true)}
                    >
                      <Text style={styles.menuButtonText}>
                        {selectedBankInfo?.label || 'Banka Seçin'}
                      </Text>
                      <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
                    </TouchableOpacity>
                  }
                >
                  {supportedBanks.map((bank) => (
                    <Menu.Item
                      key={bank.value}
                      onPress={() => {
                        setSelectedBank(bank.value);
                        setShowBankMenu(false);
                      }}
                      title={bank.label}
                    />
                  ))}
                </Menu>
                {selectedBankInfo && (
                  <Text style={styles.selectionDescription}>
                    {selectedBankInfo.description}
                  </Text>
                )}
              </Card.Content>
            </Card>

            {/* Account Selection */}
            <Card style={styles.selectionCard}>
              <Card.Content>
                <Text style={styles.selectionTitle}>Hesap Seçimi</Text>
                <Menu
                  visible={showAccountMenu}
                  onDismiss={() => setShowAccountMenu(false)}
                  anchor={
                    <TouchableOpacity
                      style={styles.menuButton}
                      onPress={() => setShowAccountMenu(true)}
                    >
                      <Text style={styles.menuButtonText}>
                        {selectedAccountInfo?.name || 'Hesap Seçin'}
                      </Text>
                      <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
                    </TouchableOpacity>
                  }
                >
                  {accountState.accounts.map((account) => (
                    <Menu.Item
                      key={account.id}
                      onPress={() => {
                        setSelectedAccount(account.id!);
                        setShowAccountMenu(false);
                      }}
                      title={account.name}
                    />
                  ))}
                </Menu>
              </Card.Content>
            </Card>

            <Button
              mode="contained"
              onPress={handleFileUpload}
              loading={loading}
              disabled={loading || !selectedAccount}
              style={styles.uploadButton}
              icon="cloud-upload"
            >
              {loading ? 'Yükleniyor...' : 'PDF Yükle ve Ayrıştır'}
            </Button>
          </View>
        ) : (
          <View style={styles.previewSection}>
            {/* Summary Card */}
            {summary && (
              <Card style={styles.summaryCard}>
                <Card.Content>
                  <Text style={styles.summaryTitle}>Ekstre Özeti</Text>
                  <Text style={styles.filename}>{filename}</Text>
                  
                  <View style={styles.summaryRow}>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>Toplam Gelir</Text>
                      <Text style={[styles.summaryValue, styles.income]}>
                        {CurrencyFormatter.format(summary.totalIncome)}
                      </Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text style={styles.summaryLabel}>Toplam Gider</Text>
                      <Text style={[styles.summaryValue, styles.expense]}>
                        {CurrencyFormatter.format(summary.totalExpense)}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryLabel}>Net Tutar</Text>
                    <Text style={[
                      styles.summaryValue,
                      summary.netAmount >= 0 ? styles.income : styles.expense
                    ]}>
                      {CurrencyFormatter.format(summary.netAmount)}
                    </Text>
                  </View>
                  
                  <Text style={styles.transactionCount}>
                    {summary.transactionCount} işlem bulundu
                  </Text>
                </Card.Content>
              </Card>
            )}

            {/* Selection Controls */}
            <Card style={styles.controlsCard}>
              <Card.Content>
                <View style={styles.selectionControls}>
                  <Text style={styles.selectionInfo}>
                    {selectedEntries.size} / {parsedEntries.length} işlem seçili
                  </Text>
                  <View style={styles.selectionButtons}>
                    <Button
                      mode="outlined"
                      onPress={selectAllEntries}
                      style={styles.selectionButton}
                      compact
                    >
                      Tümünü Seç
                    </Button>
                    <Button
                      mode="outlined"
                      onPress={deselectAllEntries}
                      style={styles.selectionButton}
                      compact
                    >
                      Hiçbirini Seçme
                    </Button>
                  </View>
                </View>
              </Card.Content>
            </Card>

            {/* Entries List */}
            <Card style={styles.entriesCard}>
              <Card.Content>
                <Text style={styles.entriesTitle}>İşlemler</Text>
                <FlatList
                  data={parsedEntries}
                  renderItem={renderEntryItem}
                  keyExtractor={(_, index) => index.toString()}
                  style={styles.entriesList}
                  scrollEnabled={false}
                />
              </Card.Content>
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => setShowPreview(false)}
                style={styles.cancelButton}
                disabled={loading}
              >
                İptal
              </Button>
              <Button
                mode="contained"
                onPress={handleImport}
                loading={loading}
                disabled={loading || selectedEntries.size === 0}
                style={styles.importButton}
              >
                {loading ? 'İçe Aktarılıyor...' : 'İçe Aktar'}
              </Button>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  uploadSection: {
    padding: 16,
  },
  instructionCard: {
    marginBottom: 16,
    elevation: 2,
  },
  instructionHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  instructionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  selectionCard: {
    marginBottom: 16,
    elevation: 2,
  },
  selectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  selectionDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  menuButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 12,
    backgroundColor: '#fff',
  },
  menuButtonText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  uploadButton: {
    backgroundColor: '#2ECC71',
    marginTop: 8,
  },
  previewSection: {
    padding: 16,
  },
  summaryCard: {
    marginBottom: 16,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  filename: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  income: {
    color: '#2ECC71',
  },
  expense: {
    color: '#E74C3C',
  },
  transactionCount: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  controlsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  selectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectionInfo: {
    fontSize: 14,
    color: '#666',
  },
  selectionButtons: {
    flexDirection: 'row',
  },
  selectionButton: {
    marginLeft: 8,
  },
  entriesCard: {
    marginBottom: 16,
    elevation: 2,
  },
  entriesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  entriesList: {
    maxHeight: 400,
  },
  entryItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingVertical: 8,
  },
  entryItemSelected: {
    backgroundColor: '#F0F8FF',
  },
  entryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  entryDetails: {
    flex: 1,
    marginLeft: 8,
  },
  entryDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  entryDate: {
    fontSize: 12,
    color: '#666',
  },
  entryAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  entryType: {
    fontSize: 12,
    color: '#666',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  importButton: {
    flex: 1,
    marginLeft: 8,
    backgroundColor: '#2ECC71',
  },
});

export default BankStatementScreen;
