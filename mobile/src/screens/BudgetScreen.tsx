import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Appbar, FAB, Card, ProgressBar, Modal, Portal, TextInput, Button, Menu } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

import { useBudgets } from '../providers/BudgetProvider';
import { useCategories } from '../providers/CategoryProvider';
import { Budget, BudgetInput } from '../services/BudgetService';
import { CurrencyFormatter } from '../utils';

const BudgetScreen: React.FC = () => {
  const { state: budgetState, loadBudgets, createBudget, updateBudget, deleteBudget, calculateBudgetProgress, getBudgetColor } = useBudgets();
  const { state: categoryState, loadCategories } = useCategories();

  const [refreshing, setRefreshing] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingBudget, setEditingBudget] = useState<Budget | null>(null);
  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [periodMenuVisible, setPeriodMenuVisible] = useState(false);

  const [formData, setFormData] = useState({
    amount: '',
    period: 'monthly' as 'monthly' | 'yearly',
    category_id: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        loadBudgets(),
        loadCategories(),
      ]);
    } catch (error) {
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const openModal = (budget?: Budget) => {
    if (budget) {
      setEditingBudget(budget);
      setFormData({
        amount: budget.amount ? budget.amount.toString() : '',
        period: budget.period || 'monthly',
        category_id: budget.categoryId || '',
      });
    } else {
      setEditingBudget(null);
      setFormData({
        amount: '',
        period: 'monthly',
        category_id: '',
      });
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingBudget(null);
    setCategoryMenuVisible(false);
    setPeriodMenuVisible(false);
  };

  const handleSave = async () => {
    if (!formData.amount.trim() || !formData.category_id) {
      Alert.alert('Hata', 'Lütfen tüm alanları doldurun.');
      return;
    }

    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Hata', 'Geçerli bir tutar girin.');
      return;
    }

    try {
      const budgetData: BudgetInput = {
        amount,
        period: formData.period,
        category_id: formData.category_id,
      };

      if (editingBudget) {
        await updateBudget(editingBudget.id!, budgetData);
        Alert.alert('Başarılı', 'Bütçe güncellendi.');
      } else {
        await createBudget(budgetData);
        Alert.alert('Başarılı', 'Bütçe oluşturuldu.');
      }

      closeModal();
    } catch (error) {
      Alert.alert('Hata', 'Bütçe kaydedilirken bir hata oluştu.');
    }
  };

  const handleDelete = (budget: Budget) => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${budget.categoryName}" kategorisi için belirlenen bütçeyi silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteBudget(budget.id!);
              Alert.alert('Başarılı', 'Bütçe silindi.');
            } catch (error) {
              Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
            }
          },
        },
      ]
    );
  };

  const selectedCategory = categoryState.categories.find(c => c.id === formData.category_id);
  const expenseCategories = categoryState.categories.filter(c => c.type === 'expense');

  const renderBudgetCard = (budget: Budget) => {
    const progress = calculateBudgetProgress(budget);
    const color = getBudgetColor(budget);

    return (
      <Card style={styles.budgetCard}>
        <Card.Content>
          <View style={styles.budgetHeader}>
            <View style={styles.budgetInfo}>
              <Text style={styles.categoryName}>{budget.categoryName}</Text>
              <Text style={styles.periodText}>
                {budget.period === 'monthly' ? 'Aylık' : 'Yıllık'} bütçe
              </Text>
            </View>
            <View style={styles.budgetActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => openModal(budget)}
              >
                <MaterialIcons name="edit" size={20} color="#666" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleDelete(budget)}
              >
                <MaterialIcons name="delete" size={20} color="#E74C3C" />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.budgetAmounts}>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Bütçe:</Text>
              <Text style={styles.budgetAmount}>
                {CurrencyFormatter.format(budget.amount)}
              </Text>
            </View>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Harcanan:</Text>
              <Text style={[styles.spentAmount, { color }]}>
                {CurrencyFormatter.format(Number(budget.spent) || 0)}
              </Text>
            </View>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Kalan:</Text>
              <Text style={[styles.remainingAmount, { color: progress.isOverBudget ? '#E74C3C' : '#2ECC71' }]}>
                {CurrencyFormatter.format(Number(budget.remaining) || 0)}
              </Text>
            </View>
          </View>

          <View style={styles.progressContainer}>
            <ProgressBar
              progress={progress.percentage / 100}
              color={color}
              style={styles.progressBar}
            />
            <Text style={[styles.progressText, { color }]}>
              {progress.percentage.toFixed(1)}%
              {progress.isOverBudget && ' (Bütçe aşıldı)'}
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {budgetState.loading && budgetState.budgets.length === 0 ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Bütçeler yükleniyor...</Text>
          </View>
        ) : budgetState.budgets.length === 0 ? (
          <View style={styles.emptyState}>
            <MaterialIcons name="account-balance-wallet" size={64} color="#ccc" />
            <Text style={styles.emptyText}>
              Henüz bütçe belirlenmemiş
            </Text>
            <Text style={styles.emptySubtext}>
              İlk bütçenizi oluşturun ve harcamalarınızı takip edin
            </Text>
          </View>
        ) : (
          <View style={styles.budgetList}>
            {budgetState.budgets.map((budget) => (
              <View key={budget.id}>
                {renderBudgetCard(budget)}
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="plus"
        label="Bütçe Ekle"
        onPress={() => openModal()}
      />

      {/* Budget Modal */}
      <Portal>
        <Modal
          visible={showModal}
          onDismiss={closeModal}
          contentContainerStyle={styles.modal}
        >
          <Text style={styles.modalTitle}>
            {editingBudget ? 'Bütçe Düzenle' : 'Yeni Bütçe'}
          </Text>

          <TextInput
            label="Bütçe Tutarı"
            value={formData.amount}
            onChangeText={(text) => setFormData(prev => ({ ...prev, amount: text }))}
            keyboardType="numeric"
            mode="outlined"
            style={styles.input}
            right={<TextInput.Affix text="₺" />}
          />

          {/* Category Selection */}
          <Text style={styles.fieldLabel}>Kategori</Text>
          <Menu
            visible={categoryMenuVisible}
            onDismiss={() => setCategoryMenuVisible(false)}
            anchor={
              <TouchableOpacity
                style={styles.menuButton}
                onPress={() => setCategoryMenuVisible(true)}
              >
                <Text style={styles.menuButtonText}>
                  {selectedCategory?.name || 'Kategori Seçin'}
                </Text>
                <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
              </TouchableOpacity>
            }
          >
            {expenseCategories.map((category) => (
              <Menu.Item
                key={category.id}
                onPress={() => {
                  setFormData(prev => ({ ...prev, category_id: category.id! }));
                  setCategoryMenuVisible(false);
                }}
                title={category.name}
              />
            ))}
          </Menu>

          {/* Period Selection */}
          <Text style={styles.fieldLabel}>Dönem</Text>
          <Menu
            visible={periodMenuVisible}
            onDismiss={() => setPeriodMenuVisible(false)}
            anchor={
              <TouchableOpacity
                style={styles.menuButton}
                onPress={() => setPeriodMenuVisible(true)}
              >
                <Text style={styles.menuButtonText}>
                  {formData.period === 'monthly' ? 'Aylık' : 'Yıllık'}
                </Text>
                <MaterialIcons name="arrow-drop-down" size={24} color="#666" />
              </TouchableOpacity>
            }
          >
            <Menu.Item
              onPress={() => {
                setFormData(prev => ({ ...prev, period: 'monthly' }));
                setPeriodMenuVisible(false);
              }}
              title="Aylık"
            />
            <Menu.Item
              onPress={() => {
                setFormData(prev => ({ ...prev, period: 'yearly' }));
                setPeriodMenuVisible(false);
              }}
              title="Yıllık"
            />
          </Menu>

          <View style={styles.modalActions}>
            <Button mode="outlined" onPress={closeModal} style={styles.cancelButton}>
              İptal
            </Button>
            <Button mode="contained" onPress={handleSave} style={styles.saveButton}>
              {editingBudget ? 'Güncelle' : 'Kaydet'}
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  budgetList: {
    padding: 16,
  },
  budgetCard: {
    marginBottom: 16,
    elevation: 2,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  budgetInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  periodText: {
    fontSize: 14,
    color: '#666',
  },
  budgetActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  budgetAmounts: {
    marginBottom: 16,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  amountLabel: {
    fontSize: 14,
    color: '#666',
  },
  budgetAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  spentAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  remainingAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'right',
    marginTop: 4,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2ECC71',
  },
  modal: {
    backgroundColor: 'white',
    padding: 24,
    margin: 20,
    borderRadius: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 24,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
    marginTop: 8,
  },
  menuButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 12,
    marginBottom: 16,
    backgroundColor: '#fff',
  },
  menuButtonText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
    backgroundColor: '#2ECC71',
  },
});

export default BudgetScreen;
