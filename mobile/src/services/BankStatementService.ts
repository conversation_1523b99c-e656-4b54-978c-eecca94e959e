import { AuthService } from './AuthService';
import { apiClient } from './ApiClient';
import { debugLog } from '../config/environment';
import * as DocumentPicker from 'expo-document-picker';

export interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category_id?: string;
  account_id?: string;
}

export interface ParsedStatement {
  entries: BankStatementEntry[];
  filename: string;
}

export interface BankStatementImportRequest {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category_id?: string;
  account_id?: string;
}

export type SupportedBank = 'vakifbank' | 'enpara' | 'garanti';

export class BankStatementService {
  private authService: AuthService;

  constructor() {
    this.authService = AuthService.getInstance();
  }

  async pickDocument(): Promise<DocumentPicker.DocumentPickerResult> {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
        copyToCacheDirectory: true,
        multiple: false,
      });

      return result;
    } catch (error) {
      console.error('Document picker error:', error);
      throw new Error('Dosya seçimi başarısız');
    }
  }

  async uploadAndParseStatement(
    file: { uri: string; name: string; type: string },
    bankType: SupportedBank,
    selectedAccount?: string
  ): Promise<ParsedStatement> {
    try {
      const formData = new FormData();
      
      // @ts-ignore - React Native FormData supports this
      formData.append('file', {
        uri: file.uri,
        type: file.type,
        name: file.name,
      });
      
      formData.append('bank', bankType);
      
      if (selectedAccount) {
        formData.append('account_id', selectedAccount);
      }

      const token = await this.authService.getStoredToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiClient.uploadFile<any>(
        '/bank-statements/upload',
        formData,
        token
      );

      const data = response.data;

      if (data && data.entries) {
        // Seçilen account'u tüm işlemlere ata
        const entriesWithAccount = data.entries.map((entry: BankStatementEntry) => ({
          ...entry,
          account_id: selectedAccount || entry.account_id
        }));

        return {
          entries: entriesWithAccount,
          filename: file.name
        };
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Upload and parse error:', error);
      throw error;
    }
  }

  async importTransactions(entries: BankStatementImportRequest[]): Promise<void> {
    try {
      const token = await this.authService.getStoredToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      await apiClient.post('/bank-statements/import', entries, {
        'Authorization': `Bearer ${token}`,
      });
    } catch (error) {
      console.error('Import transactions error:', error);
      throw error;
    }
  }

  getSupportedBanks(): Array<{ value: SupportedBank; label: string; description: string }> {
    return [
      {
        value: 'vakifbank',
        label: 'VakıfBank',
        description: 'VakıfBank hesap ekstreleri'
      },
      {
        value: 'enpara',
        label: 'Enpara',
        description: 'Enpara.com hesap ekstreleri'
      },
      {
        value: 'garanti',
        label: 'Garanti Bankası',
        description: 'Garanti Bankası hesap ekstreleri'
      }
    ];
  }

  getBankDisplayName(bankType: SupportedBank): string {
    const banks = this.getSupportedBanks();
    const bank = banks.find(b => b.value === bankType);
    return bank ? bank.label : bankType;
  }

  validateStatementEntry(entry: BankStatementEntry): boolean {
    return !!(
      entry.date &&
      entry.description &&
      typeof entry.amount === 'number' &&
      entry.amount !== 0 &&
      (entry.type === 'income' || entry.type === 'expense')
    );
  }

  filterValidEntries(entries: BankStatementEntry[]): BankStatementEntry[] {
    return entries.filter(entry => this.validateStatementEntry(entry));
  }

  groupEntriesByDate(entries: BankStatementEntry[]): Record<string, BankStatementEntry[]> {
    return entries.reduce((groups, entry) => {
      const date = entry.date.split('T')[0]; // Get date part only
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(entry);
      return groups;
    }, {} as Record<string, BankStatementEntry[]>);
  }

  calculateStatementSummary(entries: BankStatementEntry[]): {
    totalIncome: number;
    totalExpense: number;
    netAmount: number;
    transactionCount: number;
  } {
    const summary = entries.reduce(
      (acc, entry) => {
        if (entry.type === 'income') {
          acc.totalIncome += entry.amount;
        } else {
          acc.totalExpense += entry.amount;
        }
        acc.transactionCount += 1;
        return acc;
      },
      {
        totalIncome: 0,
        totalExpense: 0,
        netAmount: 0,
        transactionCount: 0,
      }
    );

    summary.netAmount = summary.totalIncome - summary.totalExpense;
    return summary;
  }

  // Helper method to format entries for display
  formatEntryForDisplay(entry: BankStatementEntry): {
    displayDate: string;
    displayAmount: string;
    displayType: string;
    isIncome: boolean;
  } {
    const date = new Date(entry.date);
    const displayDate = date.toLocaleDateString('tr-TR');
    const displayAmount = new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(Math.abs(entry.amount));
    
    return {
      displayDate,
      displayAmount,
      displayType: entry.type === 'income' ? 'Gelir' : 'Gider',
      isIncome: entry.type === 'income',
    };
  }

  // Helper method to detect transaction type from amount
  detectTransactionType(amount: number): 'income' | 'expense' {
    return amount > 0 ? 'income' : 'expense';
  }

  // Helper method to clean and normalize description
  normalizeDescription(description: string): string {
    return description
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[^\w\s-]/g, '') // Remove special characters except hyphens
      .substring(0, 100); // Limit length
  }
}
