import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient, ApiError } from './ApiClient';
import { debugLog } from '../config/environment';

export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export class AuthService {
  private static instance: AuthService;
  private tokenKey = 'auth_token';
  private userKey = 'auth_user';

  private constructor() {}

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      debugLog('Attempting login for user:', credentials.username);
      
      const response = await apiClient.post<AuthResponse>('/login', credentials);
      
      // Store token and user data
      await AsyncStorage.setItem(this.tokenKey, response.data.token);
      await AsyncStorage.setItem(this.userKey, JSON.stringify(response.data.user));
      
      debugLog('Login successful for user:', response.data.user.username);
      return response.data;
    } catch (error) {
      debugLog('Login error:', error);
      throw error;
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      debugLog('Attempting registration for user:', userData.username);
      
      const response = await apiClient.post<AuthResponse>('/register', userData);
      
      // Store token and user data
      await AsyncStorage.setItem(this.tokenKey, response.data.token);
      await AsyncStorage.setItem(this.userKey, JSON.stringify(response.data.user));
      
      debugLog('Registration successful for user:', response.data.user.username);
      return response.data;
    } catch (error) {
      debugLog('Registration error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      debugLog('Logging out user');
      
      // Try to notify server about logout
      try {
        const token = await this.getStoredToken();
        if (token) {
          await apiClient.post('/logout', {}, {
            'Authorization': `Bearer ${token}`
          });
        }
      } catch (error) {
        // Ignore server errors during logout
        debugLog('Server logout error (ignored):', error);
      }
      
      // Clear local storage
      await AsyncStorage.multiRemove([this.tokenKey, this.userKey]);
      debugLog('Logout successful');
    } catch (error) {
      debugLog('Logout error:', error);
      throw error;
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.tokenKey);
    } catch (error) {
      debugLog('Error getting stored token:', error);
      return null;
    }
  }

  async getStoredUser(): Promise<User | null> {
    try {
      const userJson = await AsyncStorage.getItem(this.userKey);
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      debugLog('Error getting stored user:', error);
      return null;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await this.getStoredToken();
      const user = await this.getStoredUser();
      return !!(token && user);
    } catch (error) {
      debugLog('Error checking authentication:', error);
      return false;
    }
  }

  async refreshToken(): Promise<string | null> {
    try {
      const currentToken = await this.getStoredToken();
      if (!currentToken) {
        throw new Error('No token available for refresh');
      }

      const response = await apiClient.post<{ token: string }>('/refresh', {}, {
        'Authorization': `Bearer ${currentToken}`
      });

      await AsyncStorage.setItem(this.tokenKey, response.data.token);
      debugLog('Token refreshed successfully');
      return response.data.token;
    } catch (error) {
      debugLog('Token refresh error:', error);
      // Clear invalid token
      await AsyncStorage.removeItem(this.tokenKey);
      throw error;
    }
  }

  async makeAuthenticatedRequest(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await apiClient.authenticatedRequest(url, options, token);
      
      // Return a Response-like object for backward compatibility
      return {
        ok: response.success,
        status: response.status,
        json: async () => response,
        text: async () => JSON.stringify(response),
      } as Response;
    } catch (error) {
      // Handle token expiration
      if (error instanceof Object && 'status' in error && error.status === 401) {
        try {
          // Try to refresh token
          await this.refreshToken();
          // Retry the request
          const newToken = await this.getStoredToken();
          if (newToken) {
            const response = await apiClient.authenticatedRequest(url, options, newToken);
            return {
              ok: response.success,
              status: response.status,
              json: async () => response,
              text: async () => JSON.stringify(response),
            } as Response;
          }
        } catch (refreshError) {
          debugLog('Token refresh failed, logging out:', refreshError);
          await this.logout();
        }
      }
      throw error;
    }
  }

  async validateToken(): Promise<boolean> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        return false;
      }

      const response = await apiClient.get('/validate', {
        'Authorization': `Bearer ${token}`
      });

      return response.success;
    } catch (error) {
      debugLog('Token validation error:', error);
      return false;
    }
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await apiClient.put<User>('/profile', userData, {
        'Authorization': `Bearer ${token}`
      });

      // Update stored user data
      await AsyncStorage.setItem(this.userKey, JSON.stringify(response.data));
      
      debugLog('Profile updated successfully');
      return response.data;
    } catch (error) {
      debugLog('Profile update error:', error);
      throw error;
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      await apiClient.post('/change-password', {
        currentPassword,
        newPassword
      }, {
        'Authorization': `Bearer ${token}`
      });

      debugLog('Password changed successfully');
    } catch (error) {
      debugLog('Password change error:', error);
      throw error;
    }
  }

  async deleteAccount(): Promise<void> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      await apiClient.delete('/auth/account', {
        'Authorization': `Bearer ${token}`
      });

      // Clear local storage
      await AsyncStorage.multiRemove([this.tokenKey, this.userKey]);

      debugLog('Account deleted successfully');
    } catch (error) {
      debugLog('Account deletion error:', error);
      throw error;
    }
  }

  // Demo login for development
  async demoLogin(): Promise<AuthResponse> {
    try {
      debugLog('Attempting demo login');

      const response = await apiClient.post<any>('/login', {
        username: 'demo',
        password: 'demo123'
      });

      // Backend returns {data: {token, user}, status}
      const authData = response.data.data;

      // Store token and user data
      await AsyncStorage.setItem(this.tokenKey, authData.token);
      await AsyncStorage.setItem(this.userKey, JSON.stringify(authData.user));

      debugLog('Demo login successful for user:', authData.user.username);
      return authData;
    } catch (error) {
      debugLog('Demo login error:', error);
      throw error;
    }
  }

  // Clear all stored auth data
  async clearStorage(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([this.tokenKey, this.userKey]);
      debugLog('Auth storage cleared');
    } catch (error) {
      debugLog('Error clearing auth storage:', error);
    }
  }
}
