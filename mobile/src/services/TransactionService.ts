import { AuthService } from './AuthService';
import { apiClient } from './ApiClient';
import { Transaction, TransactionInput } from '../models';
import { debugLog } from '../config/environment';
import { mockTransactions } from '../data/mockData';

export class TransactionService {
  private authService: AuthService;
  private isGuestMode: boolean = false;

  constructor(isGuestMode: boolean = false) {
    this.authService = AuthService.getInstance();
    this.isGuestMode = isGuestMode;
  }

  setGuestMode(isGuest: boolean) {
    this.isGuestMode = isGuest;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.authService.getStoredToken();
    if (!token) {
      throw new Error('Authentication required');
    }
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  async createTransaction(input: TransactionInput): Promise<Transaction> {
    try {
      debugLog('[DEBUG] Creating transaction:', input);

      // Guest mode cannot create transactions
      if (this.isGuestMode) {
        throw new Error('LOGIN_REQUIRED');
      }

      // Map mobile field names to backend field names
      const requestData = {
        title: input.title,
        type: input.type,
        amount: input.amount,
        currency: input.currency,
        category_id: input.categoryId,
        payment_method: input.paymentMethod,
        account_id: input.accountId,
        note: input.note || '',
        transaction_date: input.transactionDate.toISOString(),
        location: input.location || '',
      };

      debugLog('[DEBUG] Mapped request data:', requestData);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<any>('/transactions', requestData, headers);

      debugLog('[DEBUG] Transaction created successfully:', response.data);
      return this.parseTransactionResponse(response.data);
    } catch (error) {
      debugLog('[DEBUG] Create transaction error:', error);
      throw error;
    }
  }

  async updateTransaction(id: string, updates: Partial<TransactionInput>): Promise<Transaction> {
    try {
      debugLog('[DEBUG] Updating transaction:', id, updates);

      // Map mobile field names to backend field names
      const requestData: any = {};
      if (updates.title !== undefined) requestData.title = updates.title;
      if (updates.type !== undefined) requestData.type = updates.type;
      if (updates.amount !== undefined) requestData.amount = updates.amount;
      if (updates.currency !== undefined) requestData.currency = updates.currency;
      if (updates.categoryId !== undefined) requestData.category_id = updates.categoryId;
      if (updates.paymentMethod !== undefined) requestData.payment_method = updates.paymentMethod;
      if (updates.accountId !== undefined) requestData.account_id = updates.accountId;
      if (updates.note !== undefined) requestData.note = updates.note;
      if (updates.transactionDate !== undefined) requestData.transaction_date = updates.transactionDate.toISOString();
      if (updates.location !== undefined) requestData.location = updates.location;

      debugLog('[DEBUG] Mapped update data:', requestData);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<any>(`/transactions/${id}`, requestData, headers);

      debugLog('[DEBUG] Transaction updated successfully:', response.data);
      return this.parseTransactionResponse(response.data);
    } catch (error) {
      debugLog('[DEBUG] Update transaction error:', error);
      throw error;
    }
  }

  async deleteTransaction(id: string): Promise<void> {
    try {
      debugLog('[DEBUG] Deleting transaction:', id);
      
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/transactions/${id}`, headers);

      debugLog('[DEBUG] Transaction deleted successfully');
    } catch (error) {
      debugLog('[DEBUG] Delete transaction error:', error);
      throw error;
    }
  }

  async getTransactionById(id: string): Promise<Transaction | null> {
    try {
      debugLog('[DEBUG] Getting transaction by ID:', id);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<any>(`/transactions/${id}`, headers);

      debugLog('[DEBUG] Transaction retrieved successfully:');
      return this.parseTransactionResponse(response.data);
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      debugLog('[DEBUG] Get transaction error:', error);
      throw error;
    }
  }

  async getAllTransactions(): Promise<Transaction[]> {
    try {
      debugLog('[DEBUG] Getting all transactions');

      // Return mock data in guest mode
      if (this.isGuestMode) {
        debugLog('[DEBUG] Guest mode: returning mock transactions');
        return mockTransactions;
      }

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{
        rows: any[];
        total: number;
        page: number;
        per_page: number;
      }>('/transactions', headers);

      debugLog('[DEBUG] Transactions retrieved successfully:');

      // Parse and map backend response to frontend format
      const transactions = (response.data.rows || []).map(this.parseTransactionResponse);
      return transactions;
    } catch (error) {
      debugLog('[DEBUG] Get all transactions error:', error);
      throw error;
    }
  }

  async getTransactionsByAccount(accountId: string): Promise<Transaction[]> {
    try {
      debugLog('[DEBUG] Getting transactions by account:', accountId);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{
        rows: any[];
        total: number;
      }>(`/transactions?account_id=${accountId}`, headers);

      debugLog('[DEBUG] Transactions by account retrieved successfully:');
      return (response.data.rows || []).map(this.parseTransactionResponse);
    } catch (error) {
      debugLog('[DEBUG] Get transactions by account error:', error);
      throw error;
    }
  }

  async getTransactionsByCategory(categoryId: string): Promise<Transaction[]> {
    try {
      debugLog('[DEBUG] Getting transactions by category:', categoryId);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{
        rows: any[];
        total: number;
      }>(`/transactions?category_id=${categoryId}`, headers);

      debugLog('[DEBUG] Transactions by category retrieved successfully:');
      return (response.data.rows || []).map(this.parseTransactionResponse);
    } catch (error) {
      debugLog('[DEBUG] Get transactions by category error:', error);
      throw error;
    }
  }

  async getTransactionsByDateRange(startDate: Date, endDate: Date): Promise<Transaction[]> {
    try {
      debugLog('[DEBUG] Getting transactions by date range:', startDate, endDate);

      const headers = await this.getAuthHeaders();
      const start = startDate.toISOString().split('T')[0];
      const end = endDate.toISOString().split('T')[0];
      const response = await apiClient.get<{
        rows: any[];
        total: number;
      }>(`/transactions?start_date=${start}&end_date=${end}`, headers);

      debugLog('[DEBUG] Transactions by date range retrieved successfully:');
      return (response.data.rows || []).map(this.parseTransactionResponse);
    } catch (error) {
      debugLog('[DEBUG] Get transactions by date range error:', error);
      throw error;
    }
  }

  async getRecentTransactions(limit: number = 10): Promise<Transaction[]> {
    try {
      debugLog('[DEBUG] Getting recent transactions:', limit);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{
        rows: any[];
        total: number;
      }>(`/transactions?limit=${limit}&sort=date_desc`, headers);

      debugLog('[DEBUG] Recent transactions retrieved successfully');
      return (response.data.rows || []).map(this.parseTransactionResponse);
    } catch (error) {
      debugLog('[DEBUG] Get recent transactions error:', error);
      throw error;
    }
  }

  async getTransactionSummary(startDate?: Date, endDate?: Date): Promise<{
    totalIncome: number;
    totalExpense: number;
    netAmount: number;
    transactionCount: number;
  }> {
    try {
      debugLog('[DEBUG] Getting transaction summary');
      
      const headers = await this.getAuthHeaders();
      let url = '/transactions/summary';
      
      if (startDate && endDate) {
        const start = startDate.toISOString().split('T')[0];
        const end = endDate.toISOString().split('T')[0];
        url += `?startDate=${start}&endDate=${end}`;
      }
      
      const response = await apiClient.get<{
        totalIncome: number;
        totalExpense: number;
        netAmount: number;
        transactionCount: number;
      }>(url, headers);

      debugLog('[DEBUG] Transaction summary retrieved successfully:');
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Get transaction summary error:', error);
      throw error;
    }
  }

  // Helper method to parse backend transaction response to frontend format
  private parseTransactionResponse = (data: any): Transaction => {
    return {
      id: data.id,
      title: data.title,
      type: data.type,
      amount: data.amount,
      currency: data.currency,
      categoryId: data.category_id,
      paymentMethod: data.payment_method,
      accountId: data.account_id,
      note: data.note,
      transactionDate: data.transaction_date ? new Date(data.transaction_date) : new Date(),
      location: data.location,
      createdAt: data.created_at ? new Date(data.created_at) : undefined,
      updatedAt: data.updated_at ? new Date(data.updated_at) : undefined,
    };
  };

  // Singleton pattern
  private static instance: TransactionService;

  static getInstance(): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService();
    }
    return TransactionService.instance;
  }
}
