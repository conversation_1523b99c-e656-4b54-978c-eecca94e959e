import { AuthService } from './AuthService';
import { apiClient } from './ApiClient';
import { Account, AccountInput, defaultAccounts } from '../models';
import { debugLog } from '../config/environment';
import { mockAccounts } from '../data/mockData';

export class AccountService {
  private authService: AuthService;
  private isGuestMode: boolean = false;

  constructor(isGuestMode: boolean = false) {
    this.authService = AuthService.getInstance();
    this.isGuestMode = isGuestMode;
  }

  setGuestMode(isGuest: boolean) {
    this.isGuestMode = isGuest;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.authService.getStoredToken();
    if (!token) {
      throw new Error('Authentication required');
    }
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  async createAccount(input: AccountInput): Promise<Account> {
    try {
      debugLog('[DEBUG] Creating account:', input);
      
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<Account>('/accounts', input, headers);

      debugLog('[DEBUG] Account created successfully:', response.data);
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Create account error:', error);
      throw error;
    }
  }

  async updateAccount(id: string, updates: Partial<AccountInput>): Promise<Account> {
    try {
      debugLog('[DEBUG] Updating account:', id, updates);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<Account>(`/accounts/${id}`, updates, headers);

      debugLog('[DEBUG] Account updated successfully:', response.data);
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Update account error:', error);
      throw error;
    }
  }

  async deleteAccount(id: string): Promise<void> {
    try {
      debugLog('[DEBUG] Deleting account:', id);

      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/accounts/${id}`, headers);

      debugLog('[DEBUG] Account deleted successfully');
    } catch (error) {
      debugLog('[DEBUG] Delete account error:', error);
      throw error;
    }
  }

  async getAccountById(id: string): Promise<Account | null> {
    try {
      debugLog('[DEBUG] Getting account by ID:', id);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Account>(`/accounts/${id}`, headers);

      debugLog('[DEBUG] Account retrieved successfully:');
      return response.data;
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      debugLog('[DEBUG] Get account error:', error);
      throw error;
    }
  }

  async getAllAccounts(): Promise<Account[]> {
    try {
      debugLog('[DEBUG] Getting all accounts');

      // Return mock data in guest mode
      if (this.isGuestMode) {
        debugLog('[DEBUG] Guest mode: returning mock accounts');
        return mockAccounts;
      }

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Account[]>('/accounts', headers);

      debugLog('[DEBUG] Accounts retrieved successfully:');
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Get all accounts error:', error);
      throw error;
    }
  }

  async updateAccountBalance(accountId: string, newBalance: number): Promise<void> {
    try {
      debugLog('[DEBUG] Updating account balance:', accountId, newBalance);
      
      const headers = await this.getAuthHeaders();
      await apiClient.patch(`/accounts/${accountId}/balance`, { balance: newBalance }, headers);

      debugLog('[DEBUG] Account balance updated successfully');
    } catch (error) {
      debugLog('[DEBUG] Update account balance error:', error);
      throw error;
    }
  }

  async getAccountsByType(type: string): Promise<Account[]> {
    try {
      debugLog('[DEBUG] Getting accounts by type:', type);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Account[]>(`/accounts?type=${type}`, headers);

      debugLog('[DEBUG] Accounts by type retrieved successfully:', response.data);
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Get accounts by type error:', error);
      throw error;
    }
  }

  async initializeDefaultAccounts(): Promise<void> {
    try {
      debugLog('[DEBUG] Initializing default accounts');
      
      const existingAccounts = await this.getAllAccounts();
      if (existingAccounts.length > 0) {
        debugLog('[DEBUG] Accounts already exist, skipping initialization');
        return;
      }

      for (const accountData of defaultAccounts) {
        await this.createAccount(accountData);
      }

      debugLog('[DEBUG] Default accounts initialized successfully');
    } catch (error) {
      debugLog('[DEBUG] Initialize default accounts error:', error);
      throw error;
    }
  }

  // Singleton pattern
  private static instance: AccountService;
  
  static getInstance(): AccountService {
    if (!AccountService.instance) {
      AccountService.instance = new AccountService();
    }
    return AccountService.instance;
  }
}
