import { AuthService } from './AuthService';
import { apiClient } from './ApiClient';
import { Category, CategoryInput, defaultCategories } from '../models';
import { debugLog } from '../config/environment';
import { mockCategories } from '../data/mockData';

export class CategoryService {
  private authService: AuthService;
  private isGuestMode: boolean = false;

  constructor(isGuestMode: boolean = false) {
    this.authService = AuthService.getInstance();
    this.isGuestMode = isGuestMode;
  }

  setGuestMode(isGuest: boolean) {
    this.isGuestMode = isGuest;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.authService.getStoredToken();
    if (!token) {
      throw new Error('Authentication required');
    }
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  async createCategory(input: CategoryInput): Promise<Category> {
    try {
      debugLog('[DEBUG] Creating category:', input);
      
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<Category>('/categories', input, headers);

      debugLog('[DEBUG] Category created successfully:', response.data);
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Create category error:', error);
      throw error;
    }
  }

  async updateCategory(id: string, updates: Partial<CategoryInput>): Promise<Category> {
    try {
      debugLog('[DEBUG] Updating category:', id, updates);
      
      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<Category>(`/categories/${id}`, updates, headers);

      debugLog('[DEBUG] Category updated successfully:', response.data);
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Update category error:', error);
      throw error;
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      debugLog('[DEBUG] Deleting category:', id);
      
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/categories/${id}`, headers);

      debugLog('[DEBUG] Category deleted successfully');
    } catch (error) {
      debugLog('[DEBUG] Delete category error:', error);
      throw error;
    }
  }

  async getCategoryById(id: string): Promise<Category | null> {
    try {
      debugLog('[DEBUG] Getting category by ID:', id);
      
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Category>(`/categories/${id}`, headers);

      debugLog('[DEBUG] Category retrieved successfully:');
      return response.data;
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      debugLog('[DEBUG] Get category error:', error);
      throw error;
    }
  }

  async getAllCategories(): Promise<Category[]> {
    try {
      debugLog('[DEBUG] Getting all categories');

      // Return mock data in guest mode
      if (this.isGuestMode) {
        debugLog('[DEBUG] Guest mode: returning mock categories');
        return mockCategories;
      }

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Category[]>('/categories', headers);

      debugLog('[DEBUG] Categories retrieved successfully');
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Get all categories error:', error);
      throw error;
    }
  }

  async getCategories(type?: 'income' | 'expense'): Promise<Category[]> {
    try {
      debugLog('[DEBUG] Getting categories with type filter:', type);

      // Return mock data in guest mode
      if (this.isGuestMode) {
        debugLog('[DEBUG] Guest mode: returning filtered mock categories');
        let categories = mockCategories;
        if (type) {
          categories = mockCategories.filter(cat => cat.type === type);
        }
        return categories;
      }

      const headers = await this.getAuthHeaders();
      let url = '/categories';
      if (type) {
        url += `?type=${type}`;
      }
      const response = await apiClient.get<Category[]>(url, headers);

      debugLog('[DEBUG] Categories retrieved successfully:');
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Get categories error:', error);
      throw error;
    }
  }

  async getCategoriesByType(type: 'income' | 'expense'): Promise<Category[]> {
    try {
      debugLog('[DEBUG] Getting categories by type:', type);

      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Category[]>(`/categories?type=${type}`, headers);

      debugLog('[DEBUG] Categories by type retrieved successfully:');
      return response.data;
    } catch (error) {
      debugLog('[DEBUG] Get categories by type error:', error);
      throw error;
    }
  }

  async initializeDefaultCategories(): Promise<void> {
    try {
      debugLog('[DEBUG] Initializing default categories');
      
      const existingCategories = await this.getAllCategories();
      if (existingCategories.length > 0) {
        debugLog('[DEBUG] Categories already exist, skipping initialization');
        return;
      }

      for (const categoryData of defaultCategories) {
        await this.createCategory(categoryData);
      }

      debugLog('[DEBUG] Default categories initialized successfully');
    } catch (error) {
      debugLog('[DEBUG] Initialize default categories error:', error);
      throw error;
    }
  }

  // Singleton pattern
  private static instance: CategoryService;
  
  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }
}
