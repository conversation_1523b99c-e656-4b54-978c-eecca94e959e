import { AuthService } from './AuthService';
import { apiClient } from './ApiClient';
import { debugLog } from '../config/environment';

export interface Budget {
  id?: string;
  amount: number;
  period: 'monthly' | 'yearly';
  categoryId: string;
  categoryName?: string;
  spent?: number;
  remaining?: number;
  percentage?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface GetBudgetsRes {
  data: Budget[];
  status: number;
}

export interface BudgetInput {
  amount: number;
  period: 'monthly' | 'yearly';
  category_id: string;
}

export class BudgetService {
  private authService: AuthService;

  constructor() {
    this.authService = AuthService.getInstance();
  }

  async createBudget(input: BudgetInput): Promise<Budget> {
    try {
      debugLog('Creating budget:', input);

      // Validate amount
      if (!input.amount || isNaN(input.amount) || input.amount <= 0) {
        throw new Error('Geçerli bir tutar giriniz');
      }

      if (!input.category_id) {
        throw new Error('<PERSON><PERSON><PERSON> seç<PERSON>z');
      }

      const token = await this.authService.getStoredToken();

      // Map mobile field names to backend field names
      const requestData = {
        amount: input.amount,
        period: input.period,
        category_id: input.category_id
      };

      await apiClient.post<any>('/budget', requestData, {
        'Authorization': `Bearer ${token}`
      });

      // Backend returns success message, not budget data
      // We need to fetch the budget list to get the created budget
      const budgets = await this.getBudgets();
      const createdBudget = budgets.find(b => b.categoryId === input.category_id && b.period === input.period);

      if (!createdBudget) {
        throw new Error('Budget created but could not retrieve it');
      }

      return createdBudget;
    } catch (error) {
      debugLog('Create budget error:', error);
      throw error;
    }
  }

  async updateBudget(id: string, updates: Partial<BudgetInput>): Promise<Budget> {
    try {
      // Validate amount if provided
      if (updates.amount !== undefined && (isNaN(updates.amount) || updates.amount <= 0)) {
        throw new Error('Geçerli bir tutar giriniz');
      }

      const token = await this.authService.getStoredToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      // Map mobile field names to backend field names
      const requestData: any = {};
      if (updates.amount !== undefined) requestData.amount = updates.amount;
      if (updates.period !== undefined) requestData.period = updates.period;
      if (updates.category_id !== undefined) requestData.category_id = updates.category_id;

      await apiClient.put<any>(`/budget/${id}`, requestData, {
        'Authorization': `Bearer ${token}`,
      });

      // Backend returns success message, not budget data
      // We need to fetch the budget list to get the updated budget
      const budgets = await this.getBudgets();
      const updatedBudget = budgets.find(b => b.id === id);

      if (!updatedBudget) {
        throw new Error('Budget updated but could not retrieve it');
      }

      return updatedBudget;
    } catch (error) {
      console.error('Update budget error:', error);
      throw error;
    }
  }

  async deleteBudget(id: string): Promise<void> {
    try {
      const token = await this.authService.getStoredToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      await apiClient.delete(`/budget/${id}`, {
        'Authorization': `Bearer ${token}`,
      });
    } catch (error) {
      console.error('Delete budget error:', error);
      throw error;
    }
  }

  async getBudgets(): Promise<Budget[]> {
    try {
      const token = await this.authService.getStoredToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiClient.get<any>('/budget', {
        'Authorization': `Bearer ${token}`,
      });

      // Backend returns {data: Budget[], status: number}
      if (response.data && Array.isArray(response.data.data)) {
        return response.data.data.map((item: any) => this.mapResponseToBudget(item));
      }

      return [];
    } catch (error) {
      console.error('Get budgets error:', error);
      throw error;
    }
  }

  async getBudgetById(id: string): Promise<Budget | null> {
    try {
      const token = await this.authService.getStoredToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiClient.get<Budget>(`/budget/${id}`, {
        'Authorization': `Bearer ${token}`,
      });

      return this.mapResponseToBudget(response.data);
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      console.error('Get budget by ID error:', error);
      throw error;
    }
  }

  async getBudgetsByCategory(categoryId: string): Promise<Budget[]> {
    try {
      const budgets = await this.getBudgets();
      return budgets.filter(budget => budget.categoryId === categoryId);
    } catch (error) {
      console.error('Get budgets by category error:', error);
      throw error;
    }
  }

  async getBudgetsByPeriod(period: 'monthly' | 'yearly'): Promise<Budget[]> {
    try {
      const budgets = await this.getBudgets();
      return budgets.filter(budget => budget.period === period);
    } catch (error) {
      console.error('Get budgets by period error:', error);
      throw error;
    }
  }

  async getBudgetSummary(): Promise<{
    totalBudget: number;
    totalSpent: number;
    totalRemaining: number;
    budgetCount: number;
    overBudgetCount: number;
  }> {
    try {
      const budgets = await this.getBudgets();
      
      const summary = budgets.reduce(
        (acc, budget) => {
          acc.totalBudget += budget.amount;
          acc.totalSpent += budget.spent || 0;
          acc.totalRemaining += budget.remaining || 0;
          acc.budgetCount += 1;
          
          if ((budget.spent || 0) > budget.amount) {
            acc.overBudgetCount += 1;
          }
          
          return acc;
        },
        {
          totalBudget: 0,
          totalSpent: 0,
          totalRemaining: 0,
          budgetCount: 0,
          overBudgetCount: 0,
        }
      );

      return summary;
    } catch (error) {
      console.error('Get budget summary error:', error);
      throw error;
    }
  }

  private mapResponseToBudget(data: any): Budget {
    const spent = data.spent || 0;
    const amount = data.amount || 0;
    const remaining = amount - spent;
    const percentage = amount > 0 ? (spent / amount) * 100 : 0;

    return {
      id: data.id,
      amount: amount,
      period: data.period,
      categoryId: data.category_id,
      categoryName: data.category_name,
      spent: spent,
      remaining: remaining,
      percentage: percentage,
      createdAt: data.created_at ? new Date(data.created_at) : undefined,
      updatedAt: data.updated_at ? new Date(data.updated_at) : undefined,
    };
  }

  // Helper method to calculate budget progress
  calculateBudgetProgress(budget: Budget): {
    percentage: number;
    status: 'safe' | 'warning' | 'danger';
    isOverBudget: boolean;
  } {
    const spent = budget.spent || 0;
    const percentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
    
    let status: 'safe' | 'warning' | 'danger' = 'safe';
    if (percentage >= 100) {
      status = 'danger';
    } else if (percentage >= 80) {
      status = 'warning';
    }

    return {
      percentage: Math.min(percentage, 100),
      status,
      isOverBudget: spent > budget.amount,
    };
  }

  // Helper method to get budget color based on progress
  getBudgetColor(budget: Budget): string {
    const progress = this.calculateBudgetProgress(budget);
    
    switch (progress.status) {
      case 'danger':
        return '#E74C3C';
      case 'warning':
        return '#F39C12';
      default:
        return '#2ECC71';
    }
  }
}
