export class CurrencyFormatter {
  static format(amount: number, currency: string = 'TRY'): string {
    try {
      return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);
    } catch (error) {
      // Fallback for unsupported currencies
      return `${amount.toFixed(2)} ${currency}`;
    }
  }

  static formatWithoutSymbol(amount: number): string {
    return new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  static formatCompact(amount: number, currency: string = 'TRY'): string {
    if (Math.abs(amount) >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M ${currency}`;
    }
    if (Math.abs(amount) >= 1000) {
      return `${(amount / 1000).toFixed(1)}K ${currency}`;
    }
    return this.format(amount, currency);
  }

  static parse(value: string): number {
    // Remove currency symbols and spaces
    const cleanValue = value.replace(/[^\d,.-]/g, '');
    // Replace Turkish decimal separator
    const normalizedValue = cleanValue.replace(',', '.');
    return parseFloat(normalizedValue) || 0;
  }

  static getSupportedCurrencies(): Array<{ code: string; name: string; symbol: string }> {
    return [
      { code: 'TRY', name: 'Turkish Lira', symbol: '₺' },
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
    ];
  }

  static getCurrencySymbol(currency: string): string {
    const currencies = this.getSupportedCurrencies();
    const found = currencies.find(c => c.code === currency);
    return found ? found.symbol : currency;
  }
}
