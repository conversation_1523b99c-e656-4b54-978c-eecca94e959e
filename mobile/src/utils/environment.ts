import { config, ENV, debugLog } from '../config/environment';

/**
 * Get the appropriate API URL based on current environment
 */
export const getApiBaseUrl = (): string => {
  return config.API_BASE_URL;
};

/**
 * Check if we're running in development mode
 */
export const isDevelopment = (): boolean => {
  return ENV.isDevelopment;
};

/**
 * Check if we're running in production mode
 */
export const isProduction = (): boolean => {
  return ENV.isProduction;
};

/**
 * Check if we're running in staging mode
 */
export const isStaging = (): boolean => {
  return ENV.isStaging;
};

/**
 * Get current environment name
 */
export const getCurrentEnvironment = (): string => {
  return ENV.current;
};

/**
 * Log only in development/debug mode
 */
export const devLog = (...args: any[]): void => {
  if (config.DEBUG_MODE) {
    console.log('[DEV]', ...args);
  }
};

/**
 * Environment-aware error logging
 */
export const logError = (error: any, context?: string): void => {
  if (config.DEBUG_MODE) {
    console.error(`[ERROR${context ? ` - ${context}` : ''}]`, error);
  }
  
  // In production, you might want to send to crash reporting service
  if (ENV.isProduction) {
    // TODO: Send to crash reporting service (Sentry, Bugsnag, etc.)
  }
};

/**
 * Environment-aware warning logging
 */
export const logWarning = (message: string, context?: string): void => {
  if (config.DEBUG_MODE) {
    console.warn(`[WARNING${context ? ` - ${context}` : ''}]`, message);
  }
};

/**
 * Get app display name based on environment
 */
export const getAppDisplayName = (): string => {
  return config.APP_NAME;
};

/**
 * Get app version
 */
export const getAppVersion = (): string => {
  return config.APP_VERSION;
};

/**
 * Check if feature is enabled based on environment
 */
export const isFeatureEnabled = (feature: string): boolean => {
  const featureFlags: Record<string, boolean> = {
    'debug-mode': config.DEBUG_MODE,
    'mock-data': ENV.isDevelopment,
    'analytics': ENV.isProduction,
    'crash-reporting': ENV.isProduction,
    'beta-features': ENV.isStaging || ENV.isDevelopment,
    'offline-mode': true, // Always enabled
    'biometric-auth': ENV.isProduction || ENV.isStaging,
  };

  return featureFlags[feature] ?? false;
};

/**
 * Get environment-specific configuration
 */
export const getEnvironmentConfig = () => {
  return {
    apiBaseUrl: config.API_BASE_URL,
    apiTimeout: config.API_TIMEOUT,
    debugMode: config.DEBUG_MODE,
    appName: config.APP_NAME,
    appVersion: config.APP_VERSION,
    environment: ENV.current,
    isDevelopment: ENV.isDevelopment,
    isProduction: ENV.isProduction,
    isStaging: ENV.isStaging,
  };
};

/**
 * Environment-aware storage keys
 */
export const getStorageKey = (key: string): string => {
  const prefix = ENV.isDevelopment ? 'dev_' : ENV.isStaging ? 'staging_' : '';
  return `${prefix}${key}`;
};

/**
 * Get appropriate timeout values based on environment
 */
export const getTimeouts = () => {
  return {
    api: config.API_TIMEOUT,
    shortOperation: ENV.isDevelopment ? 5000 : 3000,
    longOperation: ENV.isDevelopment ? 30000 : 15000,
    fileUpload: ENV.isDevelopment ? 60000 : 30000,
  };
};

/**
 * Environment-aware retry configuration
 */
export const getRetryConfig = () => {
  return {
    maxAttempts: ENV.isDevelopment ? 5 : 3,
    baseDelay: ENV.isDevelopment ? 2000 : 1000,
    maxDelay: ENV.isDevelopment ? 10000 : 5000,
  };
};

/**
 * Check if we should show debug information
 */
export const shouldShowDebugInfo = (): boolean => {
  return config.DEBUG_MODE;
};

/**
 * Get environment-specific error messages
 */
export const getErrorMessage = (error: any): string => {
  if (ENV.isDevelopment) {
    // Show detailed error in development
    return error?.message || error?.toString() || 'Unknown error occurred';
  }
  
  // Show user-friendly messages in production
  if (error?.status === 401) {
    return 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.';
  }
  
  if (error?.status === 403) {
    return 'Bu işlem için yetkiniz bulunmuyor.';
  }
  
  if (error?.status === 404) {
    return 'İstenen kaynak bulunamadı.';
  }
  
  if (error?.status >= 500) {
    return 'Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.';
  }
  
  if (error?.code === 'NETWORK_ERROR') {
    return 'İnternet bağlantınızı kontrol edin.';
  }
  
  if (error?.code === 'TIMEOUT') {
    return 'İşlem zaman aşımına uğradı. Lütfen tekrar deneyin.';
  }
  
  return 'Bir hata oluştu. Lütfen tekrar deneyin.';
};
