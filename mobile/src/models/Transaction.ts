export interface Transaction {
  id?: string;
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  categoryId: string;
  paymentMethod: string;
  accountId: string;
  note?: string;
  transactionDate: Date;
  location?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TransactionInput {
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  categoryId: string;
  paymentMethod: string;
  accountId: string;
  note?: string;
  transactionDate: Date;
  location?: string;
}

export const createTransaction = (input: TransactionInput): Transaction => {
  const now = new Date();
  return {
    ...input,
    id: generateId(),
    createdAt: now,
    updatedAt: now,
  };
};

export const updateTransaction = (
  transaction: Transaction,
  updates: Partial<TransactionInput>
): Transaction => {
  return {
    ...transaction,
    ...updates,
    updatedAt: new Date(),
  };
};

const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};
