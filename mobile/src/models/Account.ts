export interface Account {
  id?: string;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'cash' | 'investment';
  balance: number;
  currency: string;
  color: string;
  icon: string;
  isDefault: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AccountInput {
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'cash' | 'investment';
  balance: number;
  currency: string;
  color: string;
  icon: string;
  isDefault?: boolean;
}

export const createAccount = (input: AccountInput): Account => {
  const now = new Date();
  return {
    ...input,
    id: generateId(),
    isDefault: input.isDefault || false,
    createdAt: now,
    updatedAt: now,
  };
};

export const updateAccount = (
  account: Account,
  updates: Partial<AccountInput>
): Account => {
  return {
    ...account,
    ...updates,
    updatedAt: new Date(),
  };
};

const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Default accounts
export const defaultAccounts: AccountInput[] = [
  {
    name: 'Main Checking',
    type: 'checking',
    balance: 0,
    currency: 'TRY',
    color: '#2ECC71',
    icon: 'account-balance',
    isDefault: true,
  },
  {
    name: 'Savings',
    type: 'savings',
    balance: 0,
    currency: 'TRY',
    color: '#3498DB',
    icon: 'savings',
  },
  {
    name: 'Cash',
    type: 'cash',
    balance: 0,
    currency: 'TRY',
    color: '#E74C3C',
    icon: 'money',
  },
];
