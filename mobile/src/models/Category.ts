export interface Category {
  id?: string;
  name: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
  isDefault: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CategoryInput {
  name: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
  isDefault?: boolean;
}

export const createCategory = (input: CategoryInput): Category => {
  const now = new Date();
  return {
    ...input,
    id: generateId(),
    isDefault: input.isDefault || false,
    createdAt: now,
    updatedAt: now,
  };
};

export const updateCategory = (
  category: Category,
  updates: Partial<CategoryInput>
): Category => {
  return {
    ...category,
    ...updates,
    updatedAt: new Date(),
  };
};

const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Default categories
export const defaultCategories: CategoryInput[] = [
  // Expense categories
  { name: 'Food & Dining', type: 'expense', color: '#FF6B6B', icon: 'restaurant' },
  { name: 'Transportation', type: 'expense', color: '#4ECDC4', icon: 'directions-car' },
  { name: 'Shopping', type: 'expense', color: '#45B7D1', icon: 'shopping-cart' },
  { name: 'Entertainment', type: 'expense', color: '#96CEB4', icon: 'movie' },
  { name: 'Bills & Utilities', type: 'expense', color: '#FFEAA7', icon: 'receipt' },
  { name: 'Healthcare', type: 'expense', color: '#DDA0DD', icon: 'local-hospital' },
  { name: 'Education', type: 'expense', color: '#98D8C8', icon: 'school' },
  { name: 'Travel', type: 'expense', color: '#F7DC6F', icon: 'flight' },
  
  // Income categories
  { name: 'Salary', type: 'income', color: '#2ECC71', icon: 'work' },
  { name: 'Freelance', type: 'income', color: '#3498DB', icon: 'computer' },
  { name: 'Investment', type: 'income', color: '#9B59B6', icon: 'trending-up' },
  { name: 'Gift', type: 'income', color: '#E74C3C', icon: 'card-giftcard' },
  { name: 'Other Income', type: 'income', color: '#F39C12', icon: 'attach-money' },
];
