{"name": "butce360", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "expo": "~53.0.20", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-linear-gradient": "~14.1.5", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.28.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.14.1", "@react-native-community/datetimepicker": "8.4.1", "expo-updates": "~0.28.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}